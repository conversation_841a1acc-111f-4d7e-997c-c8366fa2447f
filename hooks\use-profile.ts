import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { getSupabaseClient } from '@/lib/supabase'
import type { ProfileRow } from '@/lib/supabase'

/**
 * Custom hook to fetch user profile data with React Query
 * @param userId - The user ID to fetch profile for
 * @returns React Query result object with profile data
 */
export function useProfile(userId: string | undefined) {
  const queryClient = useQueryClient()
  
  const query = useQuery<ProfileRow, Error>({
    queryKey: ['profile', userId],
    queryFn: async () => {
      if (!userId) {
        throw new Error('User ID is required')
      }
      
      const supabase = getSupabaseClient()
      let { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()
      
      // If profile doesn't exist, try to create it
      if (error && error.code === 'PGRST116') {
        console.log('Profile not found, attempting to create one...')
        // Try to get user data from auth
        const { data: authUser, error: authError } = await supabase.auth.getUser()
        
        if (authError) {
          throw authError
        }
        
        if (authUser?.user) {
          // Create a new profile
          const { data: newProfile, error: createError } = await supabase
            .from('profiles')
            .insert({
              id: userId,
              email: authUser.user.email, // Add the email field
              business_name: authUser.user.user_metadata?.business_name || 'My Business',
              currency: authUser.user.user_metadata?.currency || 'USD',
              first_name: authUser.user.user_metadata?.first_name || '',
              last_name: authUser.user.user_metadata?.last_name || '',
              display_name: authUser.user.user_metadata?.display_name || authUser.user.email?.split('@')[0] || '',
              job_title: authUser.user.user_metadata?.job_title || '',
              avatar_url: null
            })
            .select()
            .single()
          
          if (createError) {
            console.error('Error creating profile:', createError)
            throw createError
          }
          
          data = newProfile
          error = null
        }
      }
      
      if (error) {
        console.error('Error fetching profile:', error)
        throw error
      }
      
      return data
    },
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes for responsive updates
    gcTime: 5 * 60 * 1000, // 5 minutes garbage collection time
    refetchOnMount: true,
    refetchOnWindowFocus: true, // Important for tab switching
    refetchOnReconnect: true,
    refetchInterval: false,
    refetchIntervalInBackground: false,
    retry: 3, // Increased retry count
    retryDelay: (attemptIndex) => {
      return Math.min(1000 * 2 ** attemptIndex, 30000)
    },
    networkMode: 'online' // Handle offline scenarios
  })
  
  // Mutation to update profile
  const updateProfile = useMutation({
    mutationFn: async (updates: Partial<ProfileRow>) => {
      if (!userId) {
        throw new Error('User ID is required')
      }
      
      const supabase = getSupabaseClient()
      const { data, error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', userId)
        .select()
        .single()
      
      if (error) {
        throw error
      }
      
      return data
    },
    onSuccess: (data) => {
      // Update the cache with the new data
      queryClient.setQueryData(['profile', userId], data)
    }
  })
  
  return {
    ...query,
    updateProfile: updateProfile.mutate
  }
}