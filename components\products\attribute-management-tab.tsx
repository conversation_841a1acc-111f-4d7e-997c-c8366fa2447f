'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { Plus, Tag, Edit3, Trash2 } from 'lucide-react'
import { useProductAttributes } from '@/hooks/use-product-attributes'

export function AttributeManagementTab() {
  const { 
    attributes, 
    getAttributeValues, 
    fetchAttributeValues, 
    createAttribute, 
    createAttributeValue,
    loading,
    error
  } = useProductAttributes()
  
  const { toast } = useToast()
  const [selectedAttribute, setSelectedAttribute] = useState('')
  const [newAttributeName, setNewAttributeName] = useState('')
  const [newAttributeValue, setNewAttributeValue] = useState('')
  const [isCreatingAttribute, setIsCreatingAttribute] = useState(false)
  const [isCreatingValue, setIsCreatingValue] = useState(false)

  // When attribute selection changes, fetch its values
  useEffect(() => {
    if (selectedAttribute) {
      const attribute = attributes.find(attr => attr.id === selectedAttribute)
      if (attribute) {
        fetchAttributeValues(attribute.id, attribute.name)
      }
    }
  }, [selectedAttribute, attributes, fetchAttributeValues])

  const handleCreateAttribute = async () => {
    if (!newAttributeName.trim()) return

    try {
      setIsCreatingAttribute(true)
      await createAttribute(newAttributeName.trim())
      
      toast({
        title: "Attribute Created",
        description: `Attribute "${newAttributeName.trim()}" has been created successfully.`
      })
      
      setNewAttributeName('')
      setIsCreatingAttribute(false)
    } catch (error: any) {
      console.error('Error creating attribute:', error)
      toast({
        title: "Error",
        description: error.message || 'Failed to create attribute',
        variant: "destructive"
      })
    } finally {
      setIsCreatingAttribute(false)
    }
  }

  const handleCreateValue = async () => {
    if (!selectedAttribute || !newAttributeValue.trim()) return

    try {
      setIsCreatingValue(true)
      const attribute = attributes.find(attr => attr.id === selectedAttribute)
      if (attribute) {
        await createAttributeValue(attribute.id, newAttributeValue.trim())
        
        toast({
          title: "Value Added",
          description: `Value "${newAttributeValue.trim()}" has been added to "${attribute.name}".`
        })
        
        setNewAttributeValue('')
        
        // Refresh the values for this attribute
        await fetchAttributeValues(attribute.id, attribute.name)
      }
    } catch (error: any) {
      console.error('Error creating value:', error)
      toast({
        title: "Error",
        description: error.message || 'Failed to add value',
        variant: "destructive"
      })
    } finally {
      setIsCreatingValue(false)
    }
  }

  const currentAttribute = attributes.find(attr => attr.id === selectedAttribute)
  const currentValues = selectedAttribute ? getAttributeValues(currentAttribute?.name || '') : []

  return (
    <div>
      <Card>
        <CardHeader className="p-3 border-b flex flex-row items-center justify-between gap-2">
          <Input
            type="text"
            placeholder="Search attributes..."
            className="h-8 text-xs flex-1"
            // Add search state and handler here later
          />
          <Button size="sm" className="h-8" onClick={() => setIsCreatingAttribute(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Add New Attribute
          </Button>
        </CardHeader>

        <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-6 p-0 border-t">
          {/* Master List (Left Column) */}
          <div className="md:col-span-1 border-r p-3">
            <nav className="space-y-1">
              {attributes.map((attr) => (
                <button
                  key={attr.id}
                  onClick={() => setSelectedAttribute(attr.id)}
                  className={`w-full text-left p-2 rounded-md transition-colors ${
                    selectedAttribute === attr.id
                      ? 'bg-slate-100'
                      : 'hover:bg-slate-50'
                  }`}
                >
                  <p className="font-semibold text-xs text-slate-800">{attr.name}</p>
                  {/* Optional: Add usage statistics here later */}
                  {/* <p className="text-xs text-slate-500">Used in X products</p> */}
                </button>
              ))}
            </nav>
          </div>

          {/* Detail View (Right Column) */}
          <div className="md:col-span-2 p-4">
            {!selectedAttribute ? (
              <div className="text-center py-12">
                <Tag className="w-10 h-10 mx-auto text-slate-300 mb-2" />
                <p className="text-slate-500 text-sm">Select an attribute from the list</p>
                <p className="text-slate-400 text-xs">Its values and details will appear here.</p>
              </div>
            ) : (
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h4 className="text-base font-semibold text-slate-800">
                    Values for "{currentAttribute?.name}"
                  </h4>
                  {/* Add Edit/Delete buttons for the attribute itself here */}
                </div>
                
                {/* Add New Value Form */}
                <div className="flex gap-2 mb-4">
                  <Input
                    value={newAttributeValue}
                    onChange={(e) => setNewAttributeValue(e.target.value)}
                    placeholder={`Enter new value for ${currentAttribute?.name}`}
                    className="flex-1 h-8 text-xs"
                    disabled={isCreatingValue}
                  />
                  <Button size="sm" className="h-8" onClick={handleCreateValue} disabled={!newAttributeValue.trim() || isCreatingValue}>
                    {isCreatingValue ? 'Adding...' : 'Add Value'}
                  </Button>
                </div>

                {/* Values List */}
                <h3 className="text-xs font-medium mb-3 text-slate-600">Existing Values</h3>
                <ul className="space-y-2 max-h-96 overflow-y-auto">
                  {currentValues.map((value, index) => (
                    <li key={index} className="flex items-center justify-between p-2 bg-slate-50 rounded-md">
                      <p className="text-xs font-medium text-slate-700">{value.label}</p>
                      <div className="flex items-center gap-1">
                        <Button size="sm" variant="ghost" className="h-7 w-7 p-0">
                          <Edit3 className="w-3.5 h-3.5" />
                        </Button>
                        <Button size="sm" variant="ghost" className="h-7 w-7 p-0 text-red-500 hover:text-red-700">
                          <Trash2 className="w-3.5 h-3.5" />
                        </Button>
                      </div>
                    </li>
                  ))}
                </ul>
                {currentValues.length === 0 && (
                   <p className="text-xs text-slate-500 text-center py-4">No values found for this attribute.</p>
                )}
              </div>
            )}
          </div>
        </CardContent>
        {/* You can optionally add a CardFooter if needed */}
      </Card>
      
      {/* Modal/Dialog for Creating New Attribute (keep this logic separate) */}
      {isCreatingAttribute && (
          // Implement a proper Modal/Dialog component here later.
          // For now, this placeholder shows the required fields.
          <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center">
              <div className="bg-white p-6 rounded-lg shadow-xl w-full max-w-md">
                  <h3 className="text-lg font-semibold mb-4">Add New Attribute</h3>
                  <Input
                      value={newAttributeName}
                      onChange={(e) => setNewAttributeName(e.target.value)}
                      placeholder="Enter attribute name (e.g., Size, Color)"
                      className="w-full h-8"
                  />
                  <div className="flex justify-end gap-2 mt-4">
                      <Button variant="ghost" size="sm" onClick={() => setIsCreatingAttribute(false)}>Cancel</Button>
                      <Button size="sm" onClick={handleCreateAttribute}>Create</Button>
                  </div>
              </div>
          </div>
      )}
    </div>
  )
}