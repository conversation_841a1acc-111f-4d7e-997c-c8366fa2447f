'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { getSupabaseClient } from '@/lib/supabase'
import { Loader2, CheckCircle, AlertCircle } from 'lucide-react'

export default function SetupCompletePage() {
  const [isChecking, setIsChecking] = useState(true)
  const [hasOrganization, setHasOrganization] = useState(false)
  const [isCreatingOrg, setIsCreatingOrg] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { user, forceSessionAndDataRefresh } = useAuth()
  const { toast } = useToast()
  const router = useRouter()

  // Check if user has an organization
  const checkOrganization = async () => {
    if (!user) return

    try {
      const supabase = getSupabaseClient()

      // Check if user is a member of any organization
      const { data: memberData, error: memberError } = await supabase
        .from('organization_members')
        .select('organization_id')
        .eq('user_id', user.id)
        .maybeSingle()

      if (!memberError && memberData) {
        setHasOrganization(true)
        return
      }

      // Check if user owns any organization
      const { data: ownerData, error: ownerError } = await supabase
        .from('organizations')
        .select('id')
        .eq('owner_id', user.id)
        .maybeSingle()

      if (!ownerError && ownerData) {
        setHasOrganization(true)
        return
      }

      // No organization found
      setHasOrganization(false)
    } catch (error) {
      console.error('Error checking organization:', error)
      setError('Failed to check organization status')
    } finally {
      setIsChecking(false)
    }
  }

  // Create organization for user
  const createOrganization = async () => {
    if (!user) return

    setIsCreatingOrg(true)
    setError(null)

    try {
      const supabase = getSupabaseClient()

      // Get user's business name from profile
      const { data: profileData } = await supabase
        .from('profiles')
        .select('business_name')
        .eq('id', user.id)
        .single()

      const organizationName = profileData?.business_name || 
                              (user.email ? user.email.split('@')[0] : '') || 
                              'My Organization'

      // Create organization
      const { data: organization, error: orgError } = await supabase
        .from('organizations')
        .insert({
          name: organizationName,
          owner_id: user.id
        })
        .select()
        .single()

      if (orgError) {
        throw new Error(orgError.message)
      }

      // Create organization member record
      const { error: memberError } = await supabase
        .from('organization_members')
        .insert({
          organization_id: organization.id,
          user_id: user.id,
          role: 'admin',
          invited_by: user.id,
          accepted_at: new Date().toISOString()
        })

      if (memberError) {
        throw new Error(memberError.message)
      }

      setHasOrganization(true)
      toast({
        title: 'Setup Complete',
        description: 'Your organization has been created successfully.',
      })

      // Refresh session and redirect to dashboard
      await forceSessionAndDataRefresh()
      router.push('/dashboard')
    } catch (error) {
      console.error('Error creating organization:', error)
      setError(error instanceof Error ? error.message : 'Failed to create organization')
    } finally {
      setIsCreatingOrg(false)
    }
  }

  useEffect(() => {
    if (user) {
      checkOrganization()
    }
  }, [user])

  // Redirect to dashboard if organization exists
  useEffect(() => {
    if (hasOrganization && !isChecking) {
      router.push('/dashboard')
    }
  }, [hasOrganization, isChecking, router])

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <p>Please sign in to complete your setup.</p>
              <Button 
                onClick={() => router.push('/auth/signin')}
                className="mt-4"
              >
                Go to Sign In
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (isChecking) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <Loader2 className="h-8 w-8 animate-spin mx-auto" />
              <p>Checking your account setup...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (hasOrganization) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <CheckCircle className="h-8 w-8 text-green-500 mx-auto" />
              <p>Setup complete! Redirecting to dashboard...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex items-center justify-center min-h-screen">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Complete Your Setup</CardTitle>
          <CardDescription>
            We need to create your organization to complete the setup process.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <div className="flex items-center space-x-2 text-red-600">
              <AlertCircle className="h-4 w-4" />
              <p className="text-sm">{error}</p>
            </div>
          )}
          
          <Button 
            onClick={createOrganization}
            disabled={isCreatingOrg}
            className="w-full"
          >
            {isCreatingOrg ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating Organization...
              </>
            ) : (
              'Complete Setup'
            )}
          </Button>
          
          <p className="text-xs text-muted-foreground text-center">
            This will create your organization and set up your account for use.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
