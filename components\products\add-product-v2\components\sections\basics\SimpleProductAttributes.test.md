# SimpleProductAttributes Component Test Plan

## Test Cases

### 1. Attribute Dropdown Population
- Verify that the attribute type dropdown shows attributes from the database
- Verify that new attributes added by users appear in the dropdown

### 2. Value Dropdown Population
- Verify that when an attribute is selected, the value dropdown shows corresponding values
- Verify that new values added by users appear in the dropdown

### 3. Adding New Attributes
- Verify that users can add new attributes
- Verify that new attributes are stored in the database
- Verify that duplicate attribute names are prevented

### 4. Adding New Values
- Verify that users can add new values for existing attributes
- Verify that new values are stored in the database
- Verify that duplicate values are prevented

### 5. Error Handling
- Verify that error messages are displayed when database operations fail
- Verify that duplicate attribute-value combinations are prevented

### 6. User Experience
- Verify that loading states are shown during database operations
- Verify that the UI updates correctly when new data is added
- Verify that existing attributes and values are displayed correctly

## Manual Testing Steps

1. Open the add product modal
2. Check that the attribute type dropdown shows existing attributes from the database
3. Select an attribute and verify that the value dropdown shows corresponding values
4. Add a new attribute and verify it appears in the dropdown
5. Add a new value for an attribute and verify it appears in the value dropdown
6. Try to add a duplicate attribute and verify an error message is shown
7. Try to add a duplicate value and verify it's handled gracefully
8. Add an attribute-value combination and verify it appears in the selected attributes list
9. Remove an attribute-value combination and verify it's removed from the list

## Expected Results

- All dropdowns should show data from the database
- New attributes and values should be stored in the database
- Duplicate entries should be prevented
- Error messages should be displayed when appropriate
- The UI should update correctly when data changes