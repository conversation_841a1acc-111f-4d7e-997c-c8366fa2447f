// Demo file to showcase the functionality of SimpleProductAttributes
import React from 'react';
import { SimpleProductAttributes } from './SimpleProductAttributes';
import { ProductFormProvider } from '../../../context/ProductFormContext';

export default function SimpleProductAttributesDemo() {
  const handleClose = () => {
    console.log('Close dialog');
  };

  return (
    <div className="p-4">
      <h1 className="text-xl font-bold mb-4">Simple Product Attributes Demo</h1>
      <div className="max-w-3xl">
        <ProductFormProvider onClose={handleClose}>
          <SimpleProductAttributes />
        </ProductFormProvider>
      </div>
    </div>
  );
}