'use client'

import { useState, useEffect } from 'react'
import { CategoryManager } from '@/components/category-manager'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { usePersistedState } from '@/lib/use-persisted-state'
import { createSupabaseClient, type ProductRow } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { 
  Package,
  AlertTriangle,
  BarChart3,
  TrendingUp,
  Check,
  Merge,
  Download,
  FileSpreadsheet,
  FileText,
  ChevronDown
} from 'lucide-react'
import { TabNavigation, TabConfig } from '@/components/ui/tab-navigation'
import { AttributeManagementTab } from './attribute-management-tab'

interface SettingsTabProps {
  isMobile?: boolean
  className?: string
}

export function SettingsTab({ isMobile = false, className }: SettingsTabProps) {
  const [products, setProducts] = useState<ProductRow[]>([])
  const [isLoading, setIsLoading] = useState(false)
  
  // Settings state with persistence
  const [lowStockAlerts, setLowStockAlerts] = usePersistedState('settings-low-stock-alerts', true)
  const [autoBackup, setAutoBackup] = usePersistedState('settings-product-auto-backup', false)
  const [defaultCategory, setDefaultCategory] = usePersistedState('settings-default-product-category', 'General')
  const [defaultSupplier, setDefaultSupplier] = usePersistedState('settings-default-supplier', '')
  const [autoFillSupplier, setAutoFillSupplier] = usePersistedState('settings-auto-fill-supplier', true)
  const [reminderEnabled, setReminderEnabled] = usePersistedState('settings-inventory-reminder-enabled', false)
  const [receiptReminder, setReceiptReminder] = usePersistedState('settings-receipt-reminder', true)
  
  const { user, organizationId } = useAuth()
  const { toast } = useToast()
  const supabase = createSupabaseClient()

  // Load products on component mount
  useEffect(() => {
    if (user && organizationId) {
      loadProducts()
    }
  }, [user, organizationId])

  const loadProducts = async () => {
    if (!user?.id || !organizationId) return
    
    setIsLoading(true)
    try {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('organization_id', organizationId)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error loading products:', error)
        toast({
          title: "Error Loading Products",
          description: `Failed to load products: ${error.message}`,
          variant: "destructive",
        })
      } else {
        setProducts(data || [])
      }
    } catch (error) {
      console.error('Error loading products:', error)
      toast({
        title: "Unexpected Error",
        description: "Failed to load products. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCategoryUpdate = (oldCategory: string, newCategory: string) => {
    toast({
      title: "Category Updated",
      description: `Category "${oldCategory}" has been updated to "${newCategory}".`,
    })
    // Reload products to reflect changes
    loadProducts()
  }

  const handleCategoryDelete = (category: string) => {
    toast({
      title: "Category Deleted",
      description: `Category "${category}" has been deleted.`,
    })
    // Reload products to reflect changes
    loadProducts()
  }

  const handleSaveSettings = () => {
    // In a real app, you would save these to a user preferences table
    toast({
      title: "Settings Saved",
      description: "Your product preferences have been saved successfully.",
    })
  }

  // Define tabs configuration
  const tabs: TabConfig[] = [
    {
      id: 'preferences',
      label: 'Product Preferences',
      content: (
        <Card>
          <CardContent className="p-4 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Default Category */}
              <div className="space-y-2">
                <Label htmlFor="default-category" className="text-sm font-medium text-gray-700">Default Category</Label>
                <Select value={defaultCategory} onValueChange={setDefaultCategory}>
                  <SelectTrigger className="text-sm h-9">
                    <SelectValue placeholder="Select default category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="General">General</SelectItem>
                    <SelectItem value="Electronics">Electronics</SelectItem>
                    <SelectItem value="Clothing">Clothing</SelectItem>
                    <SelectItem value="Home">Home & Garden</SelectItem>
                    <SelectItem value="Beauty">Beauty & Personal Care</SelectItem>
                    <SelectItem value="Sports">Sports & Outdoors</SelectItem>
                    <SelectItem value="Toys">Toys & Games</SelectItem>
                    <SelectItem value="Books">Books & Media</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Default Supplier */}
              <div className="space-y-2">
                <Label htmlFor="default-supplier" className="text-sm font-medium text-gray-700">Default Supplier</Label>
                <Input
                  id="default-supplier"
                  value={defaultSupplier}
                  onChange={(e) => setDefaultSupplier(e.target.value)}
                  placeholder="Enter default supplier name"
                  className="text-sm h-9"
                />
              </div>
            </div>

            {/* Notification & Automation Settings */}
            <div className="space-y-4">
              <div className="border-b border-gray-200 pb-2">
                <h4 className="text-sm font-medium text-gray-900">Notifications & Automation</h4>
              </div>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="low-stock-alerts" className="text-sm font-medium text-gray-700">Low Stock Alerts</Label>
                    <p className="text-xs text-gray-500">
                      Receive notifications when products reach low stock levels
                    </p>
                  </div>
                  <Switch
                    id="low-stock-alerts"
                    checked={lowStockAlerts}
                    onCheckedChange={setLowStockAlerts}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="auto-backup" className="text-sm font-medium text-gray-700">Auto Backup</Label>
                    <p className="text-xs text-gray-500">
                      Automatically backup your product data monthly
                    </p>
                  </div>
                  <Switch
                    id="auto-backup"
                    checked={autoBackup}
                    onCheckedChange={setAutoBackup}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="auto-fill-supplier" className="text-sm font-medium text-gray-700">Auto-fill Supplier Names</Label>
                    <p className="text-xs text-gray-500">
                      Automatically suggest supplier names based on previous entries
                    </p>
                  </div>
                  <Switch
                    id="auto-fill-supplier"
                    checked={autoFillSupplier}
                    onCheckedChange={setAutoFillSupplier}
                  />
                </div>
              </div>
            </div>

            {/* Reminders */}
            <div className="space-y-4">
              <div className="border-b border-gray-200 pb-2">
                <h4 className="text-sm font-medium text-gray-900">Reminders</h4>
              </div>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="reminder-enabled" className="text-sm font-medium text-gray-700">Enable Reminders</Label>
                    <p className="text-xs text-gray-500">
                      Get reminders for inventory management tasks
                    </p>
                  </div>
                  <Switch
                    id="reminder-enabled"
                    checked={reminderEnabled}
                    onCheckedChange={setReminderEnabled}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="receipt-reminder" className="text-sm font-medium text-gray-700">Receipt Reminder</Label>
                    <p className="text-xs text-gray-500">
                      Remind to upload receipts for products without them
                    </p>
                  </div>
                  <Switch
                    id="receipt-reminder"
                    checked={receiptReminder}
                    onCheckedChange={setReceiptReminder}
                  />
                </div>
              </div>
            </div>

            <div className="pt-2">
              <Button 
                onClick={handleSaveSettings}
                className="h-9 px-3 text-sm"
              >
                Save Preferences
              </Button>
            </div>
          </CardContent>
        </Card>
      )
    },
    {
      id: 'categories',
      label: 'Category Management',
      content: (
        <Card>
          <CardContent className="p-4">
            <CategoryManager 
              expenses={products as any} // Type casting for compatibility
              onCategoryUpdate={handleCategoryUpdate}
              onCategoryDelete={handleCategoryDelete}
            />
          </CardContent>
        </Card>
      )
    },
    {
      id: 'attributes',
      label: 'Attribute Management',
      content: <AttributeManagementTab />
    }
  ]

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-4 py-3 border-b border-gray-200">
          <h3 className="text-base font-medium text-gray-900">Product Settings</h3>
        </div>
        <div className="p-4">
          <div className="flex items-center justify-center py-8">
            <div className="text-sm text-gray-500">Loading settings...</div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={className}>
      <TabNavigation 
        tabs={tabs} 
        defaultTab="preferences"
        isMobile={isMobile}
      />
    </div>
  )
}