# Product Attributes Implementation

This document explains how the product attributes functionality works in the add product modal.

## Overview

The implementation connects the product attributes dropdowns in the add product modal directly to the database, allowing users to:

1. Select from existing attributes stored in the database
2. Add new attributes that are stored in the database
3. Select from existing values for each attribute
4. Add new values that are stored in the database

## Implementation Details

### Hook: useProductAttributes

The `useProductAttributes` hook handles all database interactions:

- Fetches all product attributes for the current organization
- Fetches values for each attribute
- Provides functions to create new attributes and values
- Handles duplicate detection and error handling

### Component: SimpleProductAttributes

The `SimpleProductAttributes` component uses the hook to:

- Populate the attribute type dropdown with database values
- Dynamically fetch and show values for the selected attribute
- Allow users to add new attributes and values
- Prevent duplicate attribute-value combinations
- Show error messages when issues occur

## Data Flow

1. When the component mounts, the hook fetches all attributes from the database
2. For each attribute, the hook fetches its associated values
3. When a user selects an attribute, the component shows the corresponding values
4. When a user adds a new attribute or value, it's stored in the database
5. The UI updates automatically to show the new data

## Database Schema

The implementation uses two tables:

1. `product_attributes` - Stores attribute names
2. `product_attribute_values` - Stores values for each attribute

Both tables are organization-specific to maintain data isolation.

## Error Handling

The implementation includes:

- Duplicate attribute detection
- Duplicate value detection
- Database error handling
- User-friendly error messages
- Loading states during database operations
