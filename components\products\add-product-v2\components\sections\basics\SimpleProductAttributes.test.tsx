import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { SimpleProductAttributes } from './SimpleProductAttributes';
import { ProductFormProvider } from '../../../context/ProductFormContext';
import { useProductAttributes } from '@/hooks/use-product-attributes';

// Mock the useProductAttributes hook
jest.mock('@/hooks/use-product-attributes', () => ({
  useProductAttributes: jest.fn()
}));

// Mock the useProductForm hook
jest.mock('../../../context/ProductFormContext', () => ({
  useProductForm: () => ({
    formData: {
      custom_attributes: []
    },
    updateFormData: jest.fn()
  })
}));

// Mock other dependencies
jest.mock('@/components/ui/input', () => ({
  Input: (props: any) => <input {...props} />
}));

jest.mock('@/components/ui/button', () => ({
  Button: (props: any) => <button {...props} />
}));

jest.mock('@/components/ui/simple-dropdown', () => ({
  SimpleDropdown: (props: any) => <select {...props} />
}));

describe('SimpleProductAttributes', () => {
  beforeEach(() => {
    (useProductAttributes as jest.Mock).mockReturnValue({
      attributes: [
        { id: '1', name: 'Size', created_at: '2023-01-01', updated_at: '2023-01-01' },
        { id: '2', name: 'Color', created_at: '2023-01-01', updated_at: '2023-01-01' }
      ],
      getAttributeValues: (name: string) => {
        if (name === 'size') {
          return [
            { value: 'small', label: 'Small' },
            { value: 'medium', label: 'Medium' },
            { value: 'large', label: 'Large' }
          ];
        }
        if (name === 'color') {
          return [
            { value: 'red', label: 'Red' },
            { value: 'blue', label: 'Blue' },
            { value: 'green', label: 'Green' }
          ];
        }
        return [];
      },
      fetchAttributeValues: jest.fn(),
      createAttribute: jest.fn(),
      createAttributeValue: jest.fn(),
      loading: false,
      error: null
    });
  });

  it('renders correctly', () => {
    render(
      <ProductFormProvider onClose={jest.fn()}>
        <SimpleProductAttributes />
      </ProductFormProvider>
    );

    expect(screen.getByText('Attribute Type')).toBeInTheDocument();
    expect(screen.getByText('Value')).toBeInTheDocument();
    expect(screen.getByText('Add')).toBeInTheDocument();
  });

  it('displays attribute options from database', async () => {
    render(
      <ProductFormProvider onClose={jest.fn()}>
        <SimpleProductAttributes />
      </ProductFormProvider>
    );

    // Check that attribute options are displayed
    expect(screen.getByRole('combobox')).toBeInTheDocument();
  });
});