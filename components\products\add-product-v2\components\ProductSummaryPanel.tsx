import React from 'react'
import { useProductForm } from '../context/ProductFormContext'

export function ProductSummaryPanel() {
  return (
    <div className="flex flex-col h-full">
      {/* Header - Aligned with horizontal nav */}
      <div className="px-3 py-3 border-b border-white/10">
        <h2 className="text-xs font-medium text-white/90 mt-2.5 text-left">
          Product Summary
        </h2>
      </div>

      {/* Product Summary Content - Full Column */}
      <div className="flex-1 overflow-y-auto">
        <ProductSummary />
      </div>
    </div>
  )
}

function ProductSummary() {
  const { formData, categories, suppliers } = useProductForm();

  // Find category name by ID
  const getCategoryName = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category ? category.name : categoryId;
  };

  // Find supplier name by ID
  const getSupplierName = (supplierId: string) => {
    const supplier = suppliers.find(sup => sup.id === supplierId);
    return supplier ? supplier.name : supplierId;
  };

  // Empty state: Displays before the user enters a product name.
  if (!formData.name) {
    return (
      <div className="h-full flex items-center justify-center p-4">
        <div className="text-center">
          <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-white/10 flex items-center justify-center">
            <svg className="w-6 h-6 text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
          </div>
          <p className="text-xs text-white/70 text-shadow">
            Product details will appear here as you fill out the form.
          </p>
        </div>
      </div>
    );
  }

  // Main component render with data - Full column design
  return (
    <div className="h-full flex flex-col">
      {/* Product Header - Compact */}
      <div className="px-3 py-3 border-b border-white/10">
        <div className="flex items-center gap-2">
          <h3 className="text-base font-semibold text-white text-shadow truncate">
            {formData.name}
          </h3>
          <div className="flex items-center gap-1.5 flex-shrink-0">
            <div className={`w-1.5 h-1.5 rounded-full ${formData.has_variants ? 'bg-purple-400' : 'bg-blue-400'}`}></div>
            <span className="text-xs text-white/80 text-shadow whitespace-nowrap">
              {formData.has_variants ? 'Variable' : 'Simple'}
            </span>
          </div>
        </div>
        {formData.base_sku && (
          <p className="text-xs text-white/80 text-shadow mt-1">
            SKU: {formData.base_sku}
          </p>
        )}
        {formData.description && (
          <p className="text-xs text-white/70 text-shadow mt-1">
            {formData.description}
          </p>
        )}
      </div>

      {/* Scrollable Content Area */}
      <div className="flex-1 overflow-y-auto">
        {/* Financials Section */}
        {(formData.price !== null && formData.price !== undefined) || formData.base_cost > 0 || formData.packaging_cost > 0 ? (
          <div className="px-3 py-2.5 border-b border-white/10">
            <h4 className="text-xs font-semibold text-white/90 text-shadow mb-1.5">Financials</h4>
            <div className="space-y-1.5">
              {formData.price !== null && formData.price !== undefined && (
                <div className="flex justify-between items-center">
                  <span className="text-xs text-white/70 text-shadow">Price</span>
                  <span className="text-xs font-semibold text-green-300 text-shadow">
                    ${formData.price.toFixed(2)}
                  </span>
                </div>
              )}
              {(formData.base_cost > 0 || formData.packaging_cost > 0) && (
                <div className="flex justify-between items-center">
                  <span className="text-xs text-white/70 text-shadow">Cost</span>
                  <span className="text-xs font-medium text-orange-300 text-shadow">
                    ${((formData.base_cost || 0) + (formData.packaging_cost || 0)).toFixed(2)}
                  </span>
                </div>
              )}
              {formData.price && (formData.base_cost > 0 || formData.packaging_cost > 0) && (
                <div className="flex justify-between items-center pt-1 border-t border-white/10">
                  <span className="text-xs font-medium text-white/80 text-shadow">Profit</span>
                  <span className="text-xs font-semibold text-blue-300 text-shadow">
                    ${(formData.price - ((formData.base_cost || 0) + (formData.packaging_cost || 0))).toFixed(2)}
                  </span>
                </div>
              )}
            </div>
          </div>
        ) : null}

        {/* Attributes Section */}
        {(formData.has_variants && formData.variant_attributes.length > 0) ||
         (!formData.has_variants && (formData.size || formData.color || formData.custom_attributes.length > 0)) ? (
          <div className="px-3 py-2.5 border-b border-white/10">
            <h4 className="text-xs font-semibold text-white/90 text-shadow mb-1.5">Attributes</h4>
            <div className="space-y-1.5">
              {!formData.has_variants ? (
                // Simple product attributes
                <>
                  {formData.size && (
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-white/70 text-shadow">Size</span>
                      <span className="text-xs font-medium text-white/90 text-shadow bg-white/10 px-1.5 py-0.5 rounded">
                        {formData.size}
                      </span>
                    </div>
                  )}
                  {formData.color && (
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-white/70 text-shadow">Color</span>
                      <span className="text-xs font-medium text-white/90 text-shadow bg-white/10 px-1.5 py-0.5 rounded">
                        {formData.color}
                      </span>
                    </div>
                  )}
                  {/* Display custom attributes */}
                  {formData.custom_attributes.map((attribute, index) => (
                    // Don't show size and color again if they're in custom_attributes
                    attribute.name !== 'size' && attribute.name !== 'color' && (
                      <div key={index} className="flex justify-between items-center">
                        <span className="text-xs text-white/70 text-shadow capitalize">{attribute.name}</span>
                        <span className="text-xs font-medium text-white/90 text-shadow bg-white/10 px-1.5 py-0.5 rounded">
                          {attribute.value}
                        </span>
                      </div>
                    )
                  ))}
                </>
              ) : (
                // Variable product attributes
                formData.variant_attributes.map((attr, index) => (
                  <div key={index} className="space-y-1">
                    <span className="text-xs font-medium text-white/80 text-shadow capitalize block">{attr.name}</span>
                    <div className="flex flex-wrap gap-1">
                      {attr.values.map((value, valueIndex) => (
                        <span key={valueIndex} className="text-xs text-white/90 text-shadow bg-white/10 px-1.5 py-0.5 rounded">
                          {value}
                        </span>
                      ))}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        ) : null}

        {/* Inventory Section */}
        {formData.stock_quantity !== undefined && !formData.has_variants && (
          <div className="px-4 py-3 border-b border-white/10">
            <h4 className="text-xs font-semibold text-white/90 text-shadow mb-2">Inventory</h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-xs text-white/70 text-shadow">Stock Quantity</span>
                <span className="text-xs font-semibold text-white text-shadow">
                  {formData.stock_quantity} units
                </span>
              </div>
              {formData.low_stock_threshold && (
                <div className="flex justify-between items-center">
                  <span className="text-xs text-white/70 text-shadow">Low Stock Alert</span>
                  <span className="text-xs font-medium text-yellow-300 text-shadow">
                    {formData.low_stock_threshold} units
                  </span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Additional Info Section */}
        {(formData.brand || formData.category_id || formData.supplier || formData.description) && (
          <div className="px-4 py-3">
            <h4 className="text-xs font-semibold text-white/90 text-shadow mb-2">Additional Details</h4>
            <div className="space-y-2">
              {formData.brand && (
                <div className="flex justify-between items-center">
                  <span className="text-xs text-white/70 text-shadow">Brand</span>
                  <span className="text-xs font-medium text-white/90 text-shadow">
                    {formData.brand}
                  </span>
                </div>
              )}
              {formData.category_id && (
                <div className="flex justify-between items-center">
                  <span className="text-xs text-white/70 text-shadow">Category</span>
                  <span className="text-xs font-medium text-white/90 text-shadow">
                    {getCategoryName(formData.category_id)}
                  </span>
                </div>
              )}
              {formData.supplier && (
                <div className="flex justify-between items-center">
                  <span className="text-xs text-white/70 text-shadow">Supplier</span>
                  <span className="text-xs font-medium text-white/90 text-shadow">
                    {getSupplierName(formData.supplier)}
                  </span>
                </div>
              )}
              {/* Description moved to header section to avoid duplication */}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}