'use client'

import React from 'react'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { FileText, Palette, Ruler } from 'lucide-react'
import { useProductForm } from '../../../context/ProductFormContext'
import { designSystem, getInputClasses } from '../../../config/design-system'
import { FormField } from '../../shared/FormField'

export function AdditionalSettingsForm() {
  const { formData, updateFormData, errors } = useProductForm()

  return (
    <div className="space-y-6">
      {/* Removed Additional Settings heading and description */}

      {/* Internal Notes Field - Removed container for simplified UI */}
      <FormField
        label="Internal Notes"
        error={errors.notes}
      >
        <Textarea
          value={formData.notes || ''}
          onChange={(e) => updateFormData('notes', e.target.value)}
          placeholder="Add any internal notes, special handling instructions, or additional information..."
          rows={4}
          className={`${getInputClasses(!!errors.notes)} resize-none`}
        />
      </FormField>
    </div>
  )
}