'use client'

import { createContext, useContext, useEffect, useState, useMemo, useCallback, useRef } from 'react'
import { User, AuthError } from '@supabase/supabase-js'
import { getSupabaseClient } from '@/lib/supabase'
import { useRouter } from 'next/navigation'
import { QueryClient } from '@tanstack/react-query'

interface AuthContextType {
  user: User | null
  userRole: string | null
  organizationId: string | null
  loading: boolean
  orgDataFetched: boolean
  isOnline: boolean
  initError: string | null
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>
  signUp: (email: string, password: string, metadata?: any) => Promise<{ error: AuthError | null }>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>
  updatePassword: (newPassword: string) => Promise<{ error: AuthError | null }>
  forceSessionAndDataRefresh: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export function AuthProvider({ children, queryClient }: { children: React.ReactNode, queryClient: QueryClient }) {
  const [session, setSession] = useState<any>(null) // New session state
  const [user, setUser] = useState<User | null>(null)
  const [userRole, setUserRole] = useState<string | null>(null)
  const [organizationId, setOrganizationId] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [orgDataFetched, setOrgDataFetched] = useState(false) // Track if org data has been fetched
  const [isOnline, setIsOnline] = useState(true)
  const [initError, setInitError] = useState<string | null>(null)
  const router = useRouter()
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const lastInvalidateTimeRef = useRef<number>(0)
  const lastRefreshTimeRef = useRef<number>(0) // Track last refresh time
  const sessionEstablishedRef = useRef<boolean>(false) // Track if session has been established

  // Debounced query invalidation to prevent excessive invalidations
  const debouncedInvalidateQueries = useCallback(async () => {
    // Clear any existing timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current)
    }
    
    // Use a debounced approach to prevent multiple invalidations
    debounceTimeoutRef.current = setTimeout(async () => {
      const now = Date.now()
      // Prevent invalidating too frequently (at most once every 2 seconds)
      if (now - lastInvalidateTimeRef.current > 2000) {
        lastInvalidateTimeRef.current = now
        try {
          await queryClient.invalidateQueries()
        } catch (error) {
          console.error('Error invalidating queries:', error)
        }
      }
    }, 300) // 300ms debounce
  }, [queryClient])

  // Fetch user role and organization when user changes
  const fetchUserOrganizationData = useCallback(async (userId: string) => {
    // Set a timeout for organization data fetching
    const orgTimeout = setTimeout(() => {
      console.error('Organization data fetch timeout for user:', userId)
      setOrganizationId(null)
      setUserRole(null)
      setOrgDataFetched(true)
      setLoading(false)
    }, 8000) // 8 second timeout for org data

    try {
      const supabase = getSupabaseClient()

      // Get organization membership for the user
      const { data: memberData, error: memberError } = await supabase
        .from('organization_members')
        .select('organization_id, role')
        .eq('user_id', userId)
        .maybeSingle()

      if (!memberError && memberData) {
        clearTimeout(orgTimeout)
        setOrganizationId(memberData.organization_id)
        setUserRole(memberData.role)
        setOrgDataFetched(true)
        setLoading(false)
        return
      }

      // If no organization membership found, check if user is an owner
      const { data: ownerData, error: ownerError } = await supabase
        .from('organizations')
        .select('id')
        .eq('owner_id', userId)
        .maybeSingle()

      if (!ownerError && ownerData) {
        clearTimeout(orgTimeout)
        setOrganizationId(ownerData.id)
        setUserRole('owner')
        setOrgDataFetched(true)
        setLoading(false)
        return
      }

      // Default to null if no organization data found
      clearTimeout(orgTimeout)
      setOrganizationId(null)
      setUserRole(null)
      setOrgDataFetched(true)
      setLoading(false)
    } catch (error) {
      clearTimeout(orgTimeout)
      console.error('Error fetching user organization data:', error)
      setOrganizationId(null)
      setUserRole(null)
      setOrgDataFetched(true)
      setLoading(false)
    }
  }, [])

  // Handle user state changes (separated from auth state changes)
  useEffect(() => {
    const newUser = session?.user ?? null
    console.debug('AuthProvider: User state changing:', newUser ? 'authenticated' : 'not authenticated')
    setUser(newUser)

    // Fetch user organization data if user is authenticated
    if (newUser) {
      console.debug('AuthProvider: Fetching organization data for user:', newUser.id)
      setOrgDataFetched(false) // Reset org data fetched state
      fetchUserOrganizationData(newUser.id)
    } else {
      console.debug('AuthProvider: No user, clearing organization data')
      setOrganizationId(null)
      setUserRole(null)
      setOrgDataFetched(true) // Mark as complete when no user
      setLoading(false)
    }
  }, [session, fetchUserOrganizationData])

  // Handle navigation based on user state (separated from auth state changes)
  useEffect(() => {
    // Don't navigate while still loading or fetching org data
    if (loading || !orgDataFetched) {
      console.debug('AuthProvider: Skipping navigation - loading:', loading, 'orgDataFetched:', orgDataFetched)
      return
    }

    const currentPath = typeof window !== 'undefined' ? window.location.pathname : ''
    console.debug('AuthProvider: Navigation check - user:', !!user, 'organizationId:', organizationId, 'currentPath:', currentPath)

    if (user) {
      // Handle navigation - only redirect if needed
      // If user has no organization, redirect to setup complete page
      if (!currentPath.includes('/auth/setup-complete') && !currentPath.includes('/auth/create-organization')) {
        // Check if user has an organization
        // organizationId === null means no organization found
        if (organizationId === null) {
          console.debug('AuthProvider: User has no organization, redirecting to setup-complete')
          router.push('/auth/setup-complete')
          return
        }
      }

      // Only redirect to dashboard if we have organization data and not already on dashboard/auth pages
      if (!currentPath.startsWith('/dashboard') && !currentPath.includes('/auth/') && organizationId !== null) {
        console.debug('AuthProvider: Redirecting authenticated user to dashboard')
        router.push('/dashboard')
      }
    } else if (!user && !currentPath.includes('/auth/') && !currentPath.includes('/landing')) {
      // Redirect to sign in if not authenticated and not already on auth/landing pages
      console.debug('AuthProvider: Redirecting unauthenticated user to signin')
      router.push('/auth/signin')
    }
  }, [user, organizationId, orgDataFetched, loading, router])

  // Handle tab visibility change with proper session refresh
  const handleVisibilityChange = useCallback(async () => {
    if (document.visibilityState === 'visible') {
      console.debug('Tab became visible - checking session')
      
      try {
        const supabase = getSupabaseClient()
        
        // Only refresh session if it's been more than 5 minutes since last refresh
        const now = Date.now()
        const fiveMinutes = 5 * 60 * 1000
        
        // Prevent too frequent refreshes (at least 30 seconds between)
        if (now - lastRefreshTimeRef.current < 30000) {
          console.debug('Skipping session refresh - too soon')
          return
        }
        
        if (!localStorage.getItem('last_session_refresh') || now - parseInt(localStorage.getItem('last_session_refresh') || '0') > fiveMinutes) {
          lastRefreshTimeRef.current = now
          localStorage.setItem('last_session_refresh', now.toString())
          
          // Use a timeout to prevent hanging promises that could cause message channel errors
          const refreshPromise = supabase.auth.refreshSession()
          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Session refresh timeout')), 10000)
          )

          try {
            const { data, error } = await Promise.race([refreshPromise, timeoutPromise]) as any

            if (error) {
              console.error('Error refreshing session on tab focus:', error)
              // If refresh fails, get current session
              const { data: sessionData } = await supabase.auth.getSession()
              setSession(sessionData?.session ?? null)
            } else {
              console.debug('Session refreshed successfully on tab focus')
              setSession(data?.session ?? null)
            }
          } catch (timeoutError) {
            console.warn('Session refresh timed out, getting current session instead')
            const { data: sessionData } = await supabase.auth.getSession()
            setSession(sessionData?.session ?? null)
          }
          
          // Invalidate queries when tab becomes visible to ensure fresh data
          console.debug('Invalidating queries due to tab visibility change')
          await debouncedInvalidateQueries()
        }
      } catch (error) {
        console.error('Error handling visibility change:', error)
      }
    }
  }, [debouncedInvalidateQueries])

  // Network status handling
  useEffect(() => {
    const handleOnline = () => {
      console.debug('Network back online')
      setIsOnline(true)
      // Refresh session and invalidate queries when coming back online
      handleVisibilityChange()
    }
    
    const handleOffline = () => {
      console.debug('Network went offline')
      setIsOnline(false)
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)
    
    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [handleVisibilityChange])

  // Main auth effect - improved session persistence and reliability
  useEffect(() => {
    const supabase = getSupabaseClient()
    let mounted = true

    // Get initial session with better error handling and persistence
    const initializeAuth = async () => {
      try {
        console.debug('AuthProvider: Initializing authentication...')

        // First, try to get the session from Supabase
        const { data: { session }, error } = await supabase.auth.getSession()

        if (error) {
          console.error('Error getting initial session:', error)
          if (mounted) {
            setInitError('Failed to initialize authentication')
            setLoading(false)
            setOrgDataFetched(true) // Prevent infinite loading
          }
          return
        }

        if (mounted) {
          console.debug('AuthProvider: Session retrieved:', session ? 'authenticated' : 'not authenticated')
          setSession(session ?? null)
          sessionEstablishedRef.current = true // Mark that session has been established

          // If no session, complete loading immediately
          if (!session) {
            console.debug('AuthProvider: No session found, completing initialization')
            setLoading(false)
            setOrgDataFetched(true) // Mark as complete to prevent navigation issues
          }
          // If session exists, loading will be handled by the user state effect
        }
      } catch (error) {
        console.error('Error initializing auth:', error)
        if (mounted) {
          setSession(null)
          setInitError('Authentication initialization failed')
          setLoading(false)
          setOrgDataFetched(true) // Prevent infinite loading
        }
      }
    }

    // Set timeout to prevent indefinite loading - this should only trigger if Supabase is completely unresponsive
    const initTimeout = setTimeout(() => {
      // Only trigger timeout if we're still loading AND we haven't established a session
      if (loading && mounted && !sessionEstablishedRef.current) {
        console.error('Auth initialization timeout - Supabase session loading took too long')
        console.error('This indicates a network or Supabase connectivity issue')
        setInitError('Authentication service is not responding. Please check your internet connection.')
        if (mounted) {
          setLoading(false)
          setOrgDataFetched(true) // Force completion to prevent infinite loading
        }
      }
    }, 15000) // Reduced to 15 seconds for better UX

    initializeAuth()

    // Set up auth state change listener - this only updates session state
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      if (mounted) {
        console.debug('AuthProvider: Auth state changed:', event, session ? 'authenticated' : 'not authenticated')
        setSession(session ?? null)

        // Handle query invalidation for specific events only
        if (event === 'SIGNED_IN') {
          console.debug('AuthProvider: User signed in, invalidating queries')
          debouncedInvalidateQueries()
        } else if (event === 'SIGNED_OUT') {
          console.debug('AuthProvider: User signed out, clearing state')
          // Clear all auth-related state immediately on sign out
          setUser(null)
          setOrganizationId(null)
          setUserRole(null)
          setOrgDataFetched(true)
          setLoading(false)
        }
      }
    })

    // Set up visibility change listener
    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      mounted = false
      sessionEstablishedRef.current = false // Reset session established flag
      clearTimeout(initTimeout)
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
      subscription.unsubscribe()
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [debouncedInvalidateQueries, handleVisibilityChange]) // Removed problematic dependencies

  const signIn = async (email: string, password: string) => {
    try {
      const supabase = getSupabaseClient()
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })
      return { error }
    } catch (error) {
      console.error('Sign in error:', error)
      return { error: error as AuthError }
    }
  }

  const signUp = async (email: string, password: string, metadata?: any) => {
    try {
      const supabase = getSupabaseClient()
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata,
        },
      })
      return { error }
    } catch (error) {
      console.error('Sign up error:', error)
      return { error: error as AuthError }
    }
  }

  const signOut = async () => {
    try {
      const supabase = getSupabaseClient()
      // Clear React Query cache before signing out
      queryClient.clear()
      await supabase.auth.signOut()
      setSession(null)
      setOrganizationId(null)
      setUserRole(null)
      // Redirect to landing page after sign out
      router.push('/landing')
    } catch (error) {
      console.error('Sign out error:', error)
    }
  }

  const resetPassword = async (email: string) => {
    try {
      const supabase = getSupabaseClient()
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      })
      return { error }
    } catch (error) {
      console.error('Reset password error:', error)
      return { error: error as AuthError }
    }
  }

  const updatePassword = async (newPassword: string) => {
    try {
      const supabase = getSupabaseClient()
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      })
      return { error }
    } catch (error) {
      console.error('Update password error:', error)
      return { error: error as AuthError }
    }
  }

  const forceSessionAndDataRefresh = async () => {
    try {
      console.debug('Force session and data refresh initiated')
      
      const supabase = getSupabaseClient()
      const { data, error } = await supabase.auth.refreshSession()
      
      if (error) {
        console.error('Error refreshing session:', error)
        // Even if refresh fails, try to get current session
        const { data: sessionData } = await supabase.auth.getSession()
        setSession(sessionData?.session ?? null)
      } else {
        console.debug('Session refreshed successfully')
        setSession(data?.session ?? null)
      }
      
      // Always invalidate all queries to force fresh data
      console.debug('Invalidating all queries...')
      await debouncedInvalidateQueries()
      console.debug('All queries invalidated successfully')
      
    } catch (error) {
      console.error('Error in forceSessionAndDataRefresh:', error)
    }
  }

  // Memoize context value
  const value = useMemo(() => ({
    user,
    userRole,
    organizationId,
    loading,
    orgDataFetched,
    isOnline,
    initError,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
    forceSessionAndDataRefresh
  }), [user, userRole, organizationId, loading, orgDataFetched, isOnline, initError])

  // Show error state if initialization failed
  if (initError) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center p-6 max-w-md">
          <div className="text-2xl font-bold text-red-600 mb-2">Authentication Error</div>
          <p className="text-gray-700 mb-4">
            {initError}. Please try refreshing the page or check your internet connection.
          </p>
          <button 
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            onClick={() => window.location.reload()}
          >
            Reload Page
          </button>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center gap-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          <span className="text-lg">Initializing authentication...</span>
        </div>
      </div>
    )
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}