'use client'

import { useState, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useAuth } from '@/contexts/auth-context'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useToast } from '@/components/ui/use-toast'
import { CustomDropdown, CustomDropdownOption } from '@/components/ui/custom-dropdown'
import { SUPPORTED_CURRENCIES, type CurrencyCode } from '@/lib/currency'

export default function SignUpPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [businessName, setBusinessName] = useState('')
  const [currency, setCurrency] = useState<string>('USD') // Default to USD
  const [loading, setLoading] = useState(false)
  const { signUp } = useAuth()
  const { toast } = useToast()
  const router = useRouter()

  // Convert currencies to dropdown options dynamically (memoized)
  const CURRENCY_OPTIONS = useMemo(() => 
    Object.entries(SUPPORTED_CURRENCIES).map(
      ([code, info]) => ({
        value: code,
        label: `${code} - ${info.name} (${info.symbol})`,
      })
    ), []
  )

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (password !== confirmPassword) {
      toast({
        title: 'Error',
        description: 'Passwords do not match',
        variant: 'destructive',
      })
      return
    }

    setLoading(true)

    try {
      const { error } = await signUp(email, password, {
        business_name: businessName,
        currency: currency, // Include currency in signup metadata
      })
      
      if (error) {
        toast({
          title: 'Error',
          description: error.message,
          variant: 'destructive',
        })
      } else {
        toast({
          title: 'Success',
          description: 'Account created! Please check your email to verify your account before signing in.',
        })
        // Redirect to signin with a flag indicating they need to complete setup
        router.push('/auth/signin?setup=pending')
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Create Your Business Account</CardTitle>
        <CardDescription>
          Set up your ONKO account with business details and preferences
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="businessName">Business Name</Label>
            <Input
              id="businessName"
              type="text"
              value={businessName}
              onChange={(e) => setBusinessName(e.target.value)}
              placeholder="Your Business Name"
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="currency">Default Currency</Label>
            <CustomDropdown
              options={CURRENCY_OPTIONS}
              value={currency}
              onValueChange={setCurrency}
              placeholder="Select your currency"
              allowCustom={false}
            />
            <p className="text-xs text-muted-foreground">
              This will be used for all financial data in your account. You can change this later in settings.
            </p>
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter your password"
              required
              minLength={6}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="confirmPassword">Confirm Password</Label>
            <Input
              id="confirmPassword"
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              placeholder="Confirm your password"
              required
              minLength={6}
            />
          </div>
          <Button 
            type="submit" 
            className="w-full" 
            disabled={loading}
          >
            {loading ? 'Setting Up Your Business...' : 'Create Business Account'}
          </Button>
        </form>
        <div className="mt-4 text-center">
          <Link 
            href="/auth/signin" 
            className="text-sm text-primary hover:underline"
          >
            Already have an account? Sign in
          </Link>
        </div>
      </CardContent>
    </Card>
  )
}