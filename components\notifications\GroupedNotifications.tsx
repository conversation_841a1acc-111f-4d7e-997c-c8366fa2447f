'use client'

import { NotificationGroup } from './NotificationGroup'

interface Notification {
  id: string
  title: string
  message: string
  type: string
  created_at: string
  is_read: boolean
  is_archived: boolean
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  category?: string
  related_entity_type?: string
  related_entity_id?: string
  related_notifications?: Notification[]
}

interface GroupedNotification {
  title: string
  notifications: Notification[]
  count: number
}

interface GroupedNotificationsProps {
  groupedNotifications: GroupedNotification[]
  expandedNotifications: string[]
  toggleNotificationExpansion: (id: string) => void
  formatDate: (dateString: string) => string
  getPanelNotificationIconProps: (type: string) => {
    Icon: any
    bgClass: string
    iconClass: string
  }
  getPriorityBadge: (priority?: string) => JSX.Element | null
  formatFullDate: (dateString: string) => string
}

export function GroupedNotifications({
  groupedNotifications,
  expandedNotifications,
  toggleNotificationExpansion,
  formatDate,
  getPanelNotificationIconProps,
  getPriorityBadge,
  formatFullDate
}: GroupedNotificationsProps) {
  return (
    <div className="space-y-4">
      {groupedNotifications.map((group, groupIndex) => (
        <NotificationGroup
          key={group.title}
          group={group}
          expandedNotifications={expandedNotifications}
          toggleNotificationExpansion={toggleNotificationExpansion}
          formatDate={formatDate}
          getPanelNotificationIconProps={getPanelNotificationIconProps}
          getPriorityBadge={getPriorityBadge}
          formatFullDate={formatFullDate}
        />
      ))}
    </div>
  )
}