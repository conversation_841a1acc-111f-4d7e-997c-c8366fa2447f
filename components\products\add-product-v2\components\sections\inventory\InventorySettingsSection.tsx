'use client'

import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { designSystem, getCardClasses } from '../../../config/design-system'
import { StockManagementForm } from './StockManagementForm'
import { AdditionalSettingsForm } from './AdditionalSettingsForm'

export function InventorySettingsSection() {
  return (
    <div className="space-y-4">
      {/* Stock Management */}
      <Card className={getCardClasses()}>
        <CardContent className="p-4">
          <StockManagementForm />
        </CardContent>
      </Card>

      {/* Additional Settings */}
      <Card className={getCardClasses()}>
        <CardContent className="p-4">
          <AdditionalSettingsForm />
        </CardContent>
      </Card>
    </div>
  )
}