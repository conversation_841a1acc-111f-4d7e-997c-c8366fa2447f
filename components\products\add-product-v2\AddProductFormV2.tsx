import React from 'react'
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  Dialog<PERSON>eader, 
  DialogTitle,
  DialogDescription
} from '@/components/ui/dialog'
import { ProductFormProvider } from './context/ProductFormContext'
import { FormContent } from './components/FormContent'
import { ProductSummaryPanel } from './components/ProductSummaryPanel'

interface AddProductFormV2Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  onProductAdded?: () => void
}

export function AddProductFormV2({ open, onOpenChange, onProductAdded }: AddProductFormV2Props) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {/* Reduced padding from p-0 gap-0 to gap-0, keeping p-0 since it's already minimal */}
      <DialogContent className="max-w-[20rem] h-[70vh] p-0 gap-0 sm:max-w-[60vw]" hideCloseButton>
        <DialogHeader className="hidden">
          <DialogTitle>Add New Product (V2)</DialogTitle>
          <DialogDescription>
            Create a new product using the step-by-step form
          </DialogDescription>
        </DialogHeader>
        <ProductFormProvider onClose={() => onOpenChange(false)} onProductAdded={onProductAdded}>
          <div className="flex flex-col lg:flex-row h-full bg-white rounded-lg overflow-hidden relative">
            {/* Main Content Area - Top on mobile, Left on desktop */}
            <div className="flex-1 flex flex-col overflow-hidden">
              <FormContent />
            </div>

            {/* Product Summary Panel - Bottom on mobile, Right on desktop */}
            <div className="w-full lg:w-80 border-t lg:border-t-0 lg:border-l border-gray-200 bg-gradient-to-b from-[#172941] to-[#111427] flex flex-col lg:max-h-full max-h-64 lg:min-h-0">
              <ProductSummaryPanel />
            </div>
          </div>
        </ProductFormProvider>
      </DialogContent>
    </Dialog>
  )
}