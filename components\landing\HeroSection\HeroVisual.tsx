"use client";

const HeroVisual = () => {
  return (
    <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 shadow-xl p-6">
      <div className="mb-6">
        <h3 className="text-white text-lg font-semibold">Best-Selling Items</h3>
      </div>
      
      <div className="space-y-4">
        {[
          { name: "Premium Headphones", value: 95 },
          { name: "Wireless Keyboard", value: 80 },
          { name: "Smart Watch", value: 75 },
          { name: "Bluetooth Speaker", value: 60 }
        ].map((item, index) => (
          <div key={index}>
            <div className="flex justify-between text-slate-300 text-sm mb-1">
              <span>{item.name}</span>
              <span>{item.value}%</span>
            </div>
            <div className="h-2 bg-slate-700 rounded-full overflow-hidden">
              <div 
                className="h-full bg-white rounded-full" 
                style={{ width: `${item.value}%` }}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default HeroVisual;