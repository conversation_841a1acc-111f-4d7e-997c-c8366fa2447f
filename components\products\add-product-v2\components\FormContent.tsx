import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { useProductForm } from '../context/ProductFormContext'
import { FORM_SECTIONS, getSectionById, getNextSection, getPreviousSection } from '../config/sections'
import { useAuth } from '@/contexts/auth-context'
import { useToast } from '@/components/ui/use-toast'
import { createSupabaseClient } from '@/lib/supabase'

// Section components (we'll create these next)
import { ProductBasicsSection } from './sections/basics/ProductBasicsSection'
import { PricingVariantsSection } from './sections/pricing/PricingVariantsSection'
import { InventorySettingsSection } from './sections/inventory/InventorySettingsSection'

function SectionHeader() {
  const { currentSection } = useProductForm()
  const section = getSectionById(currentSection)

  return (
    <div className="flex items-center justify-between">
      <div>
        <h2 className="text-lg font-semibold text-gray-900">
          {section?.label || 'Unknown Section'}
        </h2>
        {section?.description && (
          <p className="text-sm text-gray-600 mt-0.5">
            {section.description}
          </p>
        )}
      </div>
      <div className="flex items-center gap-2">
        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
        <span className="text-xs text-gray-500 uppercase tracking-wide">Active</span>
      </div>
    </div>
  )
}

// Enhanced Horizontal Navigation Component with progression indicators
function HorizontalNavigation() {
  const { currentSection, setCurrentSection, isValid, errors } = useProductForm()

  return (
    <div className="pb-1 mb-4 relative">
      <nav className="flex justify-between items-center">
        {FORM_SECTIONS.map((section) => {
          const isActive = currentSection === section.id
          const isCompleted = isValid[section.id] || false
          const hasErrors = !!errors[section.id]

          return (
            <button
              key={section.id}
              type="button"
              onClick={() => setCurrentSection(section.id)}
              className={`
                px-3 py-2 text-xs font-medium transition-all duration-200 relative
                ${isActive
                  ? 'text-gray-900'
                  : isCompleted
                  ? 'text-green-600 hover:text-green-700'
                  : hasErrors
                  ? 'text-red-600 hover:text-red-700'
                  : 'text-gray-500 hover:text-gray-700'
                }
              `}
            >
              {/* Section Label - No icons */}
              <span>
                {section.label}
              </span>
              
              {/* Status indicator for completed sections */}
              {isCompleted && !hasErrors && !isActive && (
                <span className="ml-2 text-green-500">✓</span>
              )}
              
              {/* Status indicator for sections with errors */}
              {hasErrors && !isActive && (
                <span className="ml-2 text-red-500">⚠</span>
              )}
            </button>
          )
        })}
        {/* Active indicator line positioned at the bottom */}
        <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gray-200">
          <div 
            className="h-full bg-blue-500 transition-all duration-300 ease-in-out"
            style={{
              width: `${100 / FORM_SECTIONS.length}%`,
              marginLeft: `${FORM_SECTIONS.findIndex(s => s.id === currentSection) * (100 / FORM_SECTIONS.length)}%`
            }}
          ></div>
        </div>
      </nav>
    </div>
  )
}

function SectionContent() {
  const { currentSection } = useProductForm()

  switch (currentSection) {
    case 'basics':
      return <ProductBasicsSection />
    case 'pricing':
      return <PricingVariantsSection />
    case 'inventory':
      return <InventorySettingsSection />
    default:
      return (
        <div className="text-center py-12">
          <p className="text-gray-500">Section not found</p>
        </div>
      )
  }
}

function ActionButtons() {
  const { formData, resetForm, isSubmitting, setIsSubmitting, savedVariants, onClose, onProductAdded } = useProductForm()
  const { user, organizationId } = useAuth()
  const { toast } = useToast()
  const supabase = createSupabaseClient()

  const handleSave = async () => {
    if (!user || !organizationId) {
      toast({
        title: "Authentication Error",
        description: "Please sign in to create products.",
        variant: "destructive",
      })
      return
    }

    // Validate required fields
    if (!formData.name || !formData.base_sku) {
      toast({
        title: "Missing Information",
        description: "Product name and SKU are required.",
        variant: "destructive",
      })
      return
    }

    // Validate simple product pricing
    if (!formData.has_variants && !formData.price) {
      toast({
        title: "Missing Pricing",
        description: "Simple products require a selling price.",
        variant: "destructive",
      })
      return
    }

    // Validate variable product attributes
    if (formData.has_variants && formData.variant_attributes.length === 0) {
      toast({
        title: "Missing Attributes",
        description: "Variable products require at least one attribute.",
        variant: "destructive",
      })
      return
    }

    // Validate variable product variants
    if (formData.has_variants && savedVariants.length === 0) {
      toast({
        title: "Missing Variants",
        description: "Please generate and configure variants for your variable product.",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // Prepare product data
      const productPayload = {
        organization_id: organizationId,
        name: formData.name,
        description: formData.description || null,
        brand: formData.brand || null,
        supplier: formData.supplier || null, // Use supplier ID instead of name
        base_sku: formData.base_sku,
        category_id: formData.category_id || null,
        has_variants: formData.has_variants,
        track_inventory: true,
        base_cost: formData.base_cost || 0,
        packaging_cost: formData.packaging_cost || 0,
        price: formData.price,
        size: formData.size || null,
        color: formData.color || null,
        stock_quantity: formData.stock_quantity || 0,
        low_stock_threshold: formData.low_stock_threshold || 10,
        barcode: formData.barcode || null,
        sale_price: formData.sale_price || null,
        sale_start_date: formData.sale_start_date ? formData.sale_start_date.toISOString().split('T')[0] : null,
        sale_end_date: formData.sale_end_date ? formData.sale_end_date.toISOString().split('T')[0] : null,
        batch_reference: formData.batch_reference || null,
        purchase_date: formData.purchase_date ? formData.purchase_date.toISOString().split('T')[0] : null,
        notes: formData.notes || null,
      }

      // Create the product
      const { data: productData, error: productError } = await supabase
        .from('products')
        .insert(productPayload)
        .select()
        .single()

      if (productError) {
        console.error('Error creating product:', productError)
        toast({
          title: "Error",
          description: `Failed to create product: ${productError.message}`,
          variant: "destructive",
        })
        return
      }

      // Create variants if this is a variable product
      if (formData.has_variants && savedVariants.length > 0) {
        const variantData = savedVariants.map(variant => ({
          product_id: productData.id,
          organization_id: organizationId,
          sku: variant.sku,
          price: variant.price,
          base_cost: variant.base_cost,
          packaging_cost: variant.packaging_cost,
          stock_quantity: variant.quantity,
          low_stock_threshold: variant.low_stock_threshold,
          attributes: variant.attributes.reduce((acc, attr) => {
            acc[attr.name] = attr.value
            return acc
          }, {} as Record<string, string>)
        }))

        const { error: variantError } = await supabase
          .from('product_variants')
          .insert(variantData)

        if (variantError) {
          console.error('Error creating variants:', variantError)
          toast({
            title: "Warning",
            description: "Product created but some variants failed to save. Please check and add them manually.",
            variant: "destructive",
          })
        }
      }

      console.log('Product created successfully:', productData)
      toast({
        title: "Success",
        description: `Product "${formData.name}" created successfully!`,
      })

      // Reset form and close modal after successful creation
      resetForm()
      onProductAdded?.()
      onClose()
    } catch (error) {
      console.error('Unexpected error creating product:', error)
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    resetForm()
    onClose()
  }

  return (
    <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-between gap-3 sm:gap-0">
      <Button
        type="button"
        variant="outline"
        onClick={handleCancel}
        disabled={isSubmitting}
        className="h-9 px-4 text-sm order-2 sm:order-1"
      >
        Cancel
      </Button>

      <Button
        type="button"
        onClick={handleSave}
        disabled={isSubmitting || !formData.name || !formData.base_sku}
        className="h-9 px-4 text-sm bg-green-600 hover:bg-green-700 order-1 sm:order-2"
      >
        {isSubmitting ? 'Creating...' : 'Create Product'}
      </Button>
    </div>
  )
}

export function FormContent() {
  return (
    <div className="flex-1 flex flex-col bg-white overflow-hidden">
      {/* Scrollable content */}
      <div className="flex-1 overflow-y-auto px-4 py-4">
        {/* Horizontal Navigation */}
        <HorizontalNavigation />

        {/* Section Content - Added pt-2 to give some space after navigation */}
        <div className="pt-2">
          <SectionContent />
        </div>
      </div>

      {/* Action buttons footer */}
      <div className="border-t border-gray-200 px-6 py-4 bg-gray-50 flex-shrink-0">
        <ActionButtons />
      </div>
    </div>
  )
}