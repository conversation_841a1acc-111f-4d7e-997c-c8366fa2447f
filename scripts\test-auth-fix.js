/**
 * Simple script to test the authentication fixes
 * This script verifies that the profile creation includes the email field
 */

// This script is meant to be run in a Node.js environment with the Supabase client
// You would need to install the Supabase client: npm install @supabase/supabase-js

const { createClient } = require('@supabase/supabase-js');

// Configuration - you would set these as environment variables
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY environment variables');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function testProfileCreation() {
  console.log('Testing profile creation with email field...');
  
  // Create a test user
  const testEmail = `test-${Date.now()}@example.com`;
  const testPassword = 'TestPassword123!';
  
  try {
    // Sign up a new user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          first_name: 'Test',
          last_name: 'User',
          business_name: 'Test Business'
        }
      }
    });
    
    if (authError) {
      console.error('Error signing up:', authError);
      return false;
    }
    
    console.log('User signed up successfully');
    
    // Wait a moment for the trigger to fire
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check if profile was created with email field
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user?.id)
      .single();
    
    if (profileError) {
      console.error('Error fetching profile:', profileError);
      return false;
    }
    
    if (!profileData) {
      console.error('Profile not found');
      return false;
    }
    
    // Verify the email field is populated
    if (!profileData.email || profileData.email !== testEmail) {
      console.error('Email field not populated correctly');
      console.error('Expected:', testEmail);
      console.error('Actual:', profileData.email);
      return false;
    }
    
    console.log('Profile created successfully with email field');
    console.log('Profile data:', profileData);
    
    // Note: In a real test environment, you would clean up the test user here
    // For now, we'll just return success
    
    return true;
  } catch (error) {
    console.error('Error in testProfileCreation:', error);
    return false;
  }
}

async function main() {
  console.log('Starting authentication fix verification...');
  
  try {
    const profileTestPassed = await testProfileCreation();
    
    if (profileTestPassed) {
      console.log('✅ All tests passed! Authentication fixes are working correctly.');
      process.exit(0);
    } else {
      console.log('❌ Some tests failed. Please check the output above.');
      process.exit(1);
    }
  } catch (error) {
    console.error('Error during verification:', error);
    process.exit(1);
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = { testProfileCreation };