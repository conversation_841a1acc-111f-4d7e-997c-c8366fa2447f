'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { getSupabaseClient } from '@/lib/supabase'

export function SessionDebugger() {
  const { user, loading, session } = useAuth() as any
  const [debugInfo, setDebugInfo] = useState<any>({})

  useEffect(() => {
    const checkSession = async () => {
      try {
        const supabase = getSupabaseClient()
        const { data: { session }, error } = await supabase.auth.getSession()
        
        const localStorageKeys = typeof window !== 'undefined' 
          ? Object.keys(localStorage).filter(key => 
              key.includes('supabase') || key.includes('auth') || key.includes('sb-')
            )
          : []

        setDebugInfo({
          currentSession: session,
          sessionError: error,
          user: user,
          loading: loading,
          localStorageKeys: localStorageKeys,
          currentUrl: typeof window !== 'undefined' ? window.location.href : 'SSR',
          timestamp: new Date().toISOString()
        })
      } catch (error) {
        setDebugInfo({
          error: error.message,
          timestamp: new Date().toISOString()
        })
      }
    }

    checkSession()
    
    // Check every 5 seconds
    const interval = setInterval(checkSession, 5000)
    
    return () => clearInterval(interval)
  }, [user, loading])

  const handleRefreshSession = async () => {
    try {
      const supabase = getSupabaseClient()
      const { data, error } = await supabase.auth.refreshSession()
      console.log('Manual refresh result:', { data, error })
    } catch (error) {
      console.error('Manual refresh error:', error)
    }
  }

  const handleClearStorage = () => {
    if (typeof window !== 'undefined') {
      const keys = Object.keys(localStorage).filter(key => 
        key.includes('supabase') || key.includes('auth') || key.includes('sb-')
      )
      keys.forEach(key => localStorage.removeItem(key))
      console.log('Cleared storage keys:', keys)
    }
  }

  if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development') {
    return (
      <div className="fixed bottom-4 right-4 bg-black text-white p-4 rounded-lg text-xs max-w-md max-h-96 overflow-auto z-50">
        <h3 className="font-bold mb-2">Session Debug Info</h3>
        <div className="space-y-2">
          <div>
            <strong>User:</strong> {user ? user.id : 'null'}
          </div>
          <div>
            <strong>Loading:</strong> {loading ? 'true' : 'false'}
          </div>
          <div>
            <strong>Session:</strong> {debugInfo.currentSession ? 'present' : 'null'}
          </div>
          <div>
            <strong>URL:</strong> {debugInfo.currentUrl}
          </div>
          <div>
            <strong>LocalStorage Keys:</strong>
            <ul className="text-xs">
              {debugInfo.localStorageKeys?.map((key: string) => (
                <li key={key}>• {key}</li>
              ))}
            </ul>
          </div>
          <div className="space-x-2">
            <button 
              onClick={handleRefreshSession}
              className="bg-blue-600 px-2 py-1 rounded text-xs"
            >
              Refresh Session
            </button>
            <button 
              onClick={handleClearStorage}
              className="bg-red-600 px-2 py-1 rounded text-xs"
            >
              Clear Storage
            </button>
          </div>
          <div className="text-xs opacity-75">
            Last updated: {debugInfo.timestamp}
          </div>
        </div>
      </div>
    )
  }

  return null
}
