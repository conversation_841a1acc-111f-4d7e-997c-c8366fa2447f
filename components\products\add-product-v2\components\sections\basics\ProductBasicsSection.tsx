'use client'

import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { ProductTypeSelector } from './ProductTypeSelector'
import { EssentialDetailsForm } from './EssentialDetailsForm'
import { CategorySupplierForm } from './CategorySupplierForm'
import { designSystem, getCardClasses } from '../../../config/design-system'

export function ProductBasicsSection() {
  return (
    // Standardized spacing between sections to space-y-4 for consistency
    <div className="space-y-4">
      {/* Product Type Selection - Removed card container for a more compact layout */}
      <div className="px-1">
        <ProductTypeSelector />
      </div>

      {/* Essential Details */}
      <Card className={getCardClasses()}>
        <CardContent className="p-4">
          <EssentialDetailsForm />
        </CardContent>
      </Card>

      {/* Category and Supplier */}
      <Card className={getCardClasses()}>
        <CardContent className="p-4">
          <CategorySupplierForm />
        </CardContent>
      </Card>
    </div>
  )
}