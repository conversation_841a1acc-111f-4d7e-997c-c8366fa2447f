#!/usr/bin/env deno run --allow-net --allow-env

/**
 * <PERSON><PERSON><PERSON> to verify the authentication fixes
 * This script tests:
 * 1. That the auth loop issue is resolved
 * 2. That profile creation works correctly with the email field
 */

// @ts-ignore - Deno types
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.7'

// @ts-ignore - Deno types
const supabaseUrl = Deno.env.get('SUPABASE_URL')
// @ts-ignore - Deno types
const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') // Use service role key for full access

if (!supabaseUrl || !supabaseKey) {
  // @ts-ignore - Deno types
  console.error('Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY environment variables')
  // @ts-ignore - Deno types
  Deno.exit(1)
}

// @ts-ignore - Deno types
const supabase = createClient(supabaseUrl, supabaseKey)

async function testProfileCreation() {
  console.log('Testing profile creation with email field...')
  
  // Create a test user
  const testEmail = `test-${Date.now()}@example.com`
  const testPassword = 'TestPassword123!'
  
  try {
    // Sign up a new user
    // @ts-ignore - Supabase types
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          first_name: 'Test',
          last_name: 'User',
          business_name: 'Test Business'
        }
      }
    })
    
    if (authError) {
      console.error('Error signing up:', authError)
      return false
    }
    
    console.log('User signed up successfully')
    
    // Wait a moment for the trigger to fire
    // @ts-ignore - Deno types
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Check if profile was created with email field
    // @ts-ignore - Supabase types
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user?.id)
      .single()
    
    if (profileError) {
      console.error('Error fetching profile:', profileError)
      return false
    }
    
    if (!profileData) {
      console.error('Profile not found')
      return false
    }
    
    // Verify the email field is populated
    if (!profileData.email || profileData.email !== testEmail) {
      console.error('Email field not populated correctly')
      console.error('Expected:', testEmail)
      console.error('Actual:', profileData.email)
      return false
    }
    
    console.log('Profile created successfully with email field')
    
    // Clean up - delete test user
    if (authData.user?.id) {
      // @ts-ignore - Supabase types
      await supabase.auth.admin.deleteUser(authData.user.id)
      console.log('Test user cleaned up')
    }
    
    return true
  } catch (error) {
    console.error('Error in testProfileCreation:', error)
    return false
  }
}

async function main() {
  console.log('Starting authentication fix verification...')
  
  try {
    const profileTestPassed = await testProfileCreation()
    
    if (profileTestPassed) {
      console.log('✅ All tests passed! Authentication fixes are working correctly.')
    } else {
      console.log('❌ Some tests failed. Please check the output above.')
      // @ts-ignore - Deno types
      Deno.exit(1)
    }
  } catch (error) {
    console.error('Error during verification:', error)
    // @ts-ignore - Deno types
    Deno.exit(1)
  }
}

// @ts-ignore - Deno types
if (import.meta.main) {
  main()
}