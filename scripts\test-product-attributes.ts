import { getSupabaseClient } from '@/lib/supabase';

async function testProductAttributes() {
  const supabase = getSupabaseClient();
  
  // Test organization ID (replace with a valid organization ID from your database)
  const testOrganizationId = '4debdfd9-bde6-4653-a77c-181bc7ee7321';
  
  try {
    console.log('Testing product attributes functionality...');
    
    // Test fetching product attributes
    console.log('Fetching product attributes...');
    const { data: attributes, error: attributesError } = await supabase
      .from('product_attributes')
      .select('*')
      .eq('organization_id', testOrganizationId)
      .order('name');
      
    if (attributesError) {
      console.error('Error fetching product attributes:', attributesError);
      return;
    }
    
    console.log('Product attributes fetched successfully:', attributes);
    
    // If we have attributes, test fetching their values
    if (attributes && attributes.length > 0) {
      console.log('Fetching attribute values for first attribute...');
      const firstAttribute = attributes[0];
      
      const { data: values, error: valuesError } = await supabase
        .from('product_attribute_values')
        .select('value')
        .eq('organization_id', testOrganizationId)
        .eq('attribute_id', firstAttribute.id)
        .order('value');
        
      if (valuesError) {
        console.error('Error fetching attribute values:', valuesError);
        return;
      }
      
      console.log(`Values for attribute "${firstAttribute.name}":`, values);
    }
    
    // Test creating a new attribute
    console.log('Creating a new test attribute...');
    const { data: newAttribute, error: createError } = await supabase
      .from('product_attributes')
      .insert({
        organization_id: testOrganizationId,
        name: 'Test Attribute ' + Date.now()
      })
      .select()
      .single();
      
    if (createError) {
      // Handle duplicate attribute name error
      if (createError.code === '23505') {
        console.log('Attribute already exists (this is expected in testing)');
      } else {
        console.error('Error creating test attribute:', createError);
        return;
      }
    } else {
      console.log('New attribute created successfully:', newAttribute);
      
      // Test creating a value for the new attribute
      console.log('Creating a test value for the new attribute...');
      const { data: newValue, error: createValueError } = await supabase
        .from('product_attribute_values')
        .insert({
          organization_id: testOrganizationId,
          attribute_id: newAttribute.id,
          value: 'Test Value ' + Date.now()
        })
        .select()
        .single();
        
      if (createValueError) {
        // Handle duplicate value error
        if (createValueError.code === '23505') {
          console.log('Attribute value already exists (this is expected in testing)');
        } else {
          console.error('Error creating test attribute value:', createValueError);
          return;
        }
      } else {
        console.log('New attribute value created successfully:', newValue);
      }
    }
    
    console.log('All tests completed successfully!');
  } catch (error) {
    console.error('Unexpected error during testing:', error);
  }
}

// Run the test
testProductAttributes();