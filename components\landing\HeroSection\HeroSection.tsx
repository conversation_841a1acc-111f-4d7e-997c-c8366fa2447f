"use client";

import Navbar from "@/components/landing/HeroSection/Navbar";
import HeroVisual from "@/components/landing/HeroSection/HeroVisual";
import LandingButton from "@/components/landing/shared/LandingButton";

const HeroSection = () => {
  return (
    <div className="relative min-h-screen from-[#172941] to-[#111427] bg-gradient-to-b overflow-hidden">
      {/* Grid overlay */}
      <div 
        className="absolute inset-0 z-0"
        style={{
          backgroundImage: `
            linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px)
          `,
          backgroundSize: "20px 20px"
        }}
      />
      
      <div className="relative z-10 container mx-auto px-4">
        <Navbar />
        
        <div className="flex flex-col items-center justify-center min-h-[calc(100vh-80px)] py-12">
          <div className="text-center max-w-3xl">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Streamline Your Business Operations
            </h1>
            <p className="text-lg text-slate-400 mb-10">
              Manage expenses, inventory, sales, and more with our all-in-one platform designed for modern businesses.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <LandingButton href="/auth/signup" variant="primary">Start Free Trial</LandingButton>
              <LandingButton href="/auth/signin" variant="secondary">Sign In</LandingButton>
            </div>
          </div>
          
          <div className="mt-16 w-full max-w-4xl">
            <HeroVisual />
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;