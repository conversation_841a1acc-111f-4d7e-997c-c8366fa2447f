'use client'

import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { EventEmitter } from 'events'
import { toast } from '@/components/ui/use-toast'
import { useAuth } from '@/contexts/auth-context'

interface Notification {
  id: string
  title: string
  message: string
  type: string
  created_at: string
  is_read: boolean
  is_archived: boolean
  organization_id: string
}

class CentralizedNotificationService {
  private supabase: ReturnType<typeof createClientComponentClient>
  private eventEmitter = new EventEmitter()
  private channel: any = null
  private isSubscribed = false
  private currentOrganizationId: string | null = null
  private isInitializing = false
  private subscriptionPromise: Promise<void> | null = null
  private subscribers: number = 0
  private unsubscribeCallbacks: (() => void)[] = []
  private lastVisibilityChange: number = Date.now()
  private reconnectTimeout: NodeJS.Timeout | null = null

  constructor() {
    // Use createClientComponentClient for proper real-time support
    this.supabase = createClientComponentClient()
    this.initializeAuthListener()
    this.initializeVisibilityListener()
  }

  private async initializeAuthListener() {
    // Get initial session
    const { data: { session } } = await this.supabase.auth.getSession()
    if (session?.user) {
      // Get organization ID from auth context
      // Note: In a real implementation, you would need to pass this from the component
      // For now, we'll handle this in the subscription setup
    }

    // Listen for auth changes
    this.supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        // Organization ID will be set when subscribe is called
      } else if (event === 'SIGNED_OUT') {
        this.currentOrganizationId = null
        await this.cleanup()
      }
    })
  }

  // Add visibility change listener to handle tab switching
  private initializeVisibilityListener() {
    // Only initialize visibility listener in browser environment
    if (typeof document === 'undefined' || typeof window === 'undefined') {
      return () => {}; // Return empty cleanup function
    }

    const handleVisibilityChange = () => {
      const now = Date.now()
      // Debounce visibility changes to prevent excessive reconnections
      if (now - this.lastVisibilityChange < 1000) {
        return
      }
      
      this.lastVisibilityChange = now
      
      if (document.visibilityState === 'visible') {
        console.debug('Notification service: Tab became visible')
        // When tab becomes visible, reinitialize subscription if needed
        if (this.currentOrganizationId && (!this.isSubscribed || !this.channel)) {
          this.initializeSubscription()
        }
      } else {
        console.debug('Notification service: Tab became hidden')
      }
    }

    // Handle window focus as well
    const handleFocus = () => {
      console.debug('Notification service: Window focused')
      // When window gains focus, reinitialize subscription if needed
      if (this.currentOrganizationId && (!this.isSubscribed || !this.channel)) {
        this.initializeSubscription()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('focus', handleFocus)

    // Clean up listeners
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('focus', handleFocus)
    }
  }

  private async initializeSubscription() {
    // Prevent multiple simultaneous initializations
    if (this.isInitializing) {
      return this.subscriptionPromise || Promise.resolve()
    }

    this.isInitializing = true
    this.subscriptionPromise = this.setupSubscription()
    
    try {
      await this.subscriptionPromise
    } catch (error) {
      console.error('Error in subscription promise:', error)
    } finally {
      this.isInitializing = false
      this.subscriptionPromise = null
    }
  }

  private async setupSubscription() {
    try {
      // Clean up any existing subscription
      if (this.channel) {
        try {
          await this.supabase.removeChannel(this.channel)
        } catch (error) {
          console.error('Error removing existing channel:', error)
        }
        this.channel = null
        this.isSubscribed = false
        
        // Add a small delay to avoid connection conflicts
        await new Promise(resolve => setTimeout(resolve, 500))
      }

      if (!this.currentOrganizationId) {
        return
      }

      // Only refresh schema cache in development mode to reduce production overhead
      if (process.env.NODE_ENV === 'development') {
        await this.refreshSchemaCache();
      }

      this.channel = this.supabase
        .channel(`notifications:${this.currentOrganizationId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'notifications',
            filter: `organization_id=eq.${this.currentOrganizationId}`
          },
          (payload) => {
            // Emit notification for this organization
            this.eventEmitter.emit('notification-inserted', payload.new)
            
            // Show toast notification for new notifications
            this.showNotificationToast(payload.new as unknown as Notification)
          }
        )
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'notifications',
            filter: `organization_id=eq.${this.currentOrganizationId}`
          },
          (payload) => {
            // Emit notification update
            this.eventEmitter.emit('notification-updated', payload.new)
          }
        )
        .on(
          'postgres_changes',
          {
            event: 'DELETE',
            schema: 'public',
            table: 'notifications',
            filter: `organization_id=eq.${this.currentOrganizationId}`
          },
          (payload) => {
            // Emit notification deletion
            this.eventEmitter.emit('notification-deleted', payload.old)
          }
        )

      // Subscribe with proper error handling and timeout
      const subscriptionPromise = new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Subscription timeout'))
        }, 10000) // 10 second timeout

        this.channel.subscribe((status: string, err: any) => {
          clearTimeout(timeout)

          if (err) {
            console.error('Subscription error:', err)
            // Clear the channel reference on error
            this.channel = null
            this.isSubscribed = false
            reject(err)

            // Attempt to reconnect after a delay
            if (this.reconnectTimeout) {
              clearTimeout(this.reconnectTimeout)
            }
            this.reconnectTimeout = setTimeout(() => {
              if (this.currentOrganizationId) {
                this.initializeSubscription()
              }
            }, 5000)
            return
          }

          if (status === 'SUBSCRIBED') {
            this.isSubscribed = true
            console.log('Successfully subscribed to notifications')
            resolve()
          } else if (status === 'CHANNEL_ERROR') {
            this.isSubscribed = false
            this.channel = null
            console.error('Channel error occurred')
            reject(new Error('Channel error'))
          } else if (status === 'CLOSED') {
            this.isSubscribed = false
            this.channel = null
            console.log('Channel closed')
            reject(new Error('Channel closed'))
          }
        })
      })

      try {
        await subscriptionPromise
        console.log('Notification subscription setup completed successfully')
      } catch (error) {
        console.error('Subscription failed:', error)
        this.channel = null
        this.isSubscribed = false
        return
      }
      
    } catch (error) {
      console.error('Error setting up notification subscription:', error)
      this.isSubscribed = false
      this.channel = null
      
      // Attempt to reconnect after a delay
      if (this.reconnectTimeout) {
        clearTimeout(this.reconnectTimeout)
      }
      this.reconnectTimeout = setTimeout(() => {
        if (this.currentOrganizationId) {
          this.initializeSubscription()
        }
      }, 5000)
    }
  }

  // Add a method to refresh schema cache
  private async refreshSchemaCache() {
    try {
      console.log('Refreshing schema cache for notifications...')
      
      // Query the actual notifications table to force schema refresh
      const { data: notificationData, error: notificationError } = await this.supabase
        .from('notifications')
        .select('id, organization_id, title')
        .limit(1)
      
      if (notificationError) {
        console.error('Error in notifications table query:', notificationError)
      } else {
        console.log('Notifications table query successful')
      }
      
      // Also query products table to ensure schema is refreshed
      const { data: productData, error: productError } = await this.supabase
        .from('products')
        .select('id, organization_id, name')
        .limit(1)
      
      if (productError) {
        console.error('Error in products table query:', productError)
      } else {
        console.log('Products table query successful')
      }
      
      console.log('Schema cache refresh completed')
    } catch (error) {
      console.error('Error refreshing schema cache:', error)
    }
  }

  // Show toast notification for new notifications
  private showNotificationToast(notification: Notification) {
    // Validate notification data before processing
    if (!notification || !notification.id) {
      console.warn('Invalid notification received, skipping toast')
      return
    }
    
    // Validate created_at field
    if (!notification.created_at) {
      console.warn('Notification missing created_at field:', notification.id)
      return
    }
    
    const date = new Date(notification.created_at)
    if (isNaN(date.getTime())) {
      console.warn('Notification has invalid created_at field:', notification.id, notification.created_at)
      return
    }
    
    // Only show toast for unread notifications
    if (!notification.is_read) {
      toast({
        title: notification.title,
        description: notification.message,
        variant: notification.type === 'warning' ? 'destructive' : 'default'
      })
    }
  }

  // Subscribe to notification events
  subscribe(
    organizationId: string,
    onInsert: (notification: Notification) => void,
    onUpdate: (notification: Notification) => void,
    onDelete: (notification: Notification) => void
  ) {
    this.currentOrganizationId = organizationId
    this.subscribers++
    
    // Wrap the onInsert callback to validate notifications
    const validatedOnInsert = (notification: Notification) => {
      // Validate notification data before processing
      if (!notification || !notification.id) {
        console.warn('Invalid notification received, skipping insert handler')
        return
      }
      
      // Validate created_at field
      if (!notification.created_at) {
        console.warn('Notification missing created_at field:', notification.id)
        return
      }
      
      const date = new Date(notification.created_at)
      if (isNaN(date.getTime())) {
        console.warn('Notification has invalid created_at field:', notification.id, notification.created_at)
        return
      }
      
      // Call the original handler
      onInsert(notification)
    }
    
    // Wrap the onUpdate callback to validate notifications
    const validatedOnUpdate = (notification: Notification) => {
      // Validate notification data before processing
      if (!notification || !notification.id) {
        console.warn('Invalid notification received, skipping update handler')
        return
      }
      
      // Validate created_at field
      if (!notification.created_at) {
        console.warn('Notification missing created_at field:', notification.id)
        return
      }
      
      const date = new Date(notification.created_at)
      if (isNaN(date.getTime())) {
        console.warn('Notification has invalid created_at field:', notification.id, notification.created_at)
        return
      }
      
      // Call the original handler
      onUpdate(notification)
    }
    
    // Wrap the onDelete callback to validate notifications
    const validatedOnDelete = (notification: Notification) => {
      // Validate notification data before processing
      if (!notification || !notification.id) {
        console.warn('Invalid notification received, skipping delete handler')
        return
      }
      
      // Call the original handler
      onDelete(notification)
    }
    
    this.eventEmitter.on('notification-inserted', validatedOnInsert)
    this.eventEmitter.on('notification-updated', validatedOnUpdate)
    this.eventEmitter.on('notification-deleted', validatedOnDelete)

    // If we have an organization ID but no active channel, initialize subscription
    if (this.currentOrganizationId && (!this.channel || !this.isSubscribed)) {
      // Use a small delay to prevent race conditions
      setTimeout(() => {
        if (this.currentOrganizationId && (!this.channel || !this.isSubscribed)) {
          this.initializeSubscription()
        }
      }, 100)
    }

    // Return unsubscribe function
    const unsubscribe = () => {
      this.subscribers--
      this.eventEmitter.off('notification-inserted', validatedOnInsert)
      this.eventEmitter.off('notification-updated', validatedOnUpdate)
      this.eventEmitter.off('notification-deleted', validatedOnDelete)
      
      // If no more subscribers, clean up
      if (this.subscribers <= 0) {
        this.cleanup()
      }
    }
    
    this.unsubscribeCallbacks.push(unsubscribe)
    return unsubscribe
  }

  // Cleanup subscription
  async cleanup() {
    // Clear any pending reconnect timeouts
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
      this.reconnectTimeout = null
    }
    
    if (this.channel) {
      try {
        // Remove the channel properly
        const result = await this.supabase.removeChannel(this.channel)
        console.log('Channel removed:', result)
        this.channel = null
        this.isSubscribed = false
        
        // Return a promise to ensure we wait for cleanup to complete
        return new Promise<void>(resolve => setTimeout(resolve, 100))
      } catch (error) {
        console.error('Error cleaning up notification channel:', error)
        this.channel = null
        this.isSubscribed = false
      }
    }
    return Promise.resolve()
  }

  // Broadcast a custom event to all subscribers
  broadcastEvent(eventType: string, data: any) {
    this.eventEmitter.emit(eventType, data)
  }

  // Subscribe to custom events
  subscribeToEvent(eventType: string, handler: (data: any) => void) {
    this.eventEmitter.on(eventType, handler)
    
    // Return unsubscribe function
    return () => {
      this.eventEmitter.off(eventType, handler)
    }
  }

  // Get current subscription status
  getSubscriptionStatus() {
    return {
      isSubscribed: this.isSubscribed,
      hasChannel: !!this.channel,
      organizationId: this.currentOrganizationId,
      subscribers: this.subscribers
    }
  }

  // Force reconnection to handle tab switching issues
  async reconnect(organizationId: string) {
    console.debug('Notification service: Forcing reconnection')
    this.currentOrganizationId = organizationId
    
    // Clear any pending reconnect timeouts
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
      this.reconnectTimeout = null
    }
    
    await this.cleanup()
    if (this.currentOrganizationId) {
      // Add a small delay before reconnecting
      setTimeout(() => {
        this.initializeSubscription()
      }, 300)
    }
  }
}

// Export singleton instance
export const centralizedNotificationService = new CentralizedNotificationService()