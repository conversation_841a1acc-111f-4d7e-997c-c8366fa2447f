"use client"

import * as React from "react"
import { Check, ChevronDown, Plus, X } from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

export interface SimpleDropdownOption {
  value: string
  label: string
  isCustom?: boolean
  // Add a unique key property to ensure React keys are unique
  key?: string
}

interface SimpleDropdownProps {
  options: SimpleDropdownOption[]
  value?: string
  onValueChange: (value: string) => void
  onAddCustomOption?: (option: SimpleDropdownOption) => void
  placeholder?: string
  allowCustom?: boolean
  customPlaceholder?: string
  className?: string
  optionClassName?: string
  disabled?: boolean
  size?: 'default' | 'sm' | 'xs' // Add size prop
  textsize?: 'default' | 'sm' | 'xs' // Add text size prop
  showSearch?: boolean // Add showSearch prop
}

export function SimpleDropdown({
  options,
  value,
  onValueChange,
  onAddCustomOption,
  placeholder = "Select option...",
  allowCustom = true,
  customPlaceholder = "Add custom option...",
  className,
  optionClassName = "",
  disabled = false,
  size = "default", // Default size
  textsize = "default", // Default text size
  showSearch = true, // Default to showing search
}: SimpleDropdownProps) {
  const [open, setOpen] = React.useState(false)
  const [showCustomInput, setShowCustomInput] = React.useState(false)
  const [customValue, setCustomValue] = React.useState("")
  const [search, setSearch] = React.useState("")

  const selectedOption = options.find((option) => option.value === value)

  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(search.toLowerCase())
  )

  const handleOptionSelect = (optionValue: string) => {
    // Only log in development mode to reduce console noise
    if (process.env.NODE_ENV === 'development') {
      console.log('SimpleDropdown: Option selected:', optionValue)
    }
    onValueChange(optionValue)
    setOpen(false)
    setSearch("")
  }

  const handleAddCustom = () => {
    if (customValue.trim() && onAddCustomOption) {
      const newOption: SimpleDropdownOption = {
        value: customValue.trim(),
        label: customValue.trim(),
        isCustom: true
      }
      onAddCustomOption(newOption)
      onValueChange(customValue.trim())
      setCustomValue("")
      setShowCustomInput(false)
      setOpen(false)
    }
  }

  // Map size to button size variants
  const buttonSize = size === "xs" ? "sm" : size === "sm" ? "sm" : "default";
  
  // Map text size to text classes
  const textSizeClass = textsize === "xs" ? "text-xs" : textsize === "sm" ? "text-sm" : "text-sm";
  const optionTextSizeClass = textsize === "xs" ? "text-xs" : textsize === "sm" ? "text-sm" : "text-sm";
  
  // Map size to height classes
  const heightClass = size === "xs" ? "h-8" : size === "sm" ? "h-9" : "h-10";

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between", 
            textSizeClass, // Apply text size class
            heightClass, // Apply height class
            className
          )}
          size={buttonSize} // Apply button size
          disabled={disabled}
        >
          {selectedOption ? selectedOption.label : placeholder}
          <ChevronDown className={cn("ml-2 shrink-0 opacity-50", 
            size === "xs" ? "h-3 w-3" : size === "sm" ? "h-4 w-4" : "h-4 w-4"
          )} />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <div className="flex flex-col">
          {/* Search Input - Conditionally rendered */}
          {showSearch && (
            <div className="flex items-center border-b px-3">
              <Input
                placeholder="Search options..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className={cn("border-0 focus-visible:ring-0 focus-visible:ring-offset-0", optionTextSizeClass)}
              />
            </div>
          )}
          
          {/* Options List */}
          <div className="max-h-[300px] overflow-y-auto">
            {filteredOptions.length === 0 && !allowCustom ? (
              <div className={cn("py-6 text-center", optionTextSizeClass)}>No options found.</div>
            ) : (
              <>
                {filteredOptions.map((option, index) => (
                  <div
                    key={option.key || `${option.value}-${index}`}
                    onClick={() => handleOptionSelect(option.value)}
                    className={cn(
                      "flex items-center justify-between cursor-pointer px-2 py-1.5 hover:bg-accent hover:text-accent-foreground",
                      optionClassName,
                      optionTextSizeClass // Ensure text size is applied to each option
                    )}
                  >
                    <div className="flex items-center">
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          value === option.value ? "opacity-100" : "opacity-0"
                        )}
                      />
                      <span className={optionTextSizeClass}>{option.label}</span> {/* Apply text size to the label */}
                    </div>
                    {option.isCustom && (
                      <span className={cn("text-muted-foreground", optionTextSizeClass)}>Custom</span>
                    )}
                  </div>
                ))}
                
                {allowCustom && (
                  <>
                    {filteredOptions.length > 0 && (
                      <div className="border-t border-gray-200 my-1"></div>
                    )}
                    {!showCustomInput ? (
                      <div
                        key="custom-option"
                        onClick={() => setShowCustomInput(true)}
                        className={cn(
                          "flex items-center cursor-pointer px-2 py-1.5 text-primary hover:text-blue-700 hover:bg-accent",
                          optionClassName,
                          optionTextSizeClass
                        )}
                      >
                        <Plus className={cn("mr-2 h-4 w-4", optionTextSizeClass)} />
                        <span className={optionTextSizeClass}>Add custom option</span> {/* Apply text size to the label */}
                      </div>
                    ) : (
                      <div className="p-2 border-t">
                        <div className="flex items-center space-x-2">
                          <Input
                            value={customValue}
                            onChange={(e) => setCustomValue(e.target.value)}
                            placeholder="Enter custom option"
                            className={cn("flex-1", optionTextSizeClass)}
                            autoFocus
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                e.preventDefault()
                                handleAddCustom()
                              } else if (e.key === "Escape") {
                                setCustomValue("")
                                setShowCustomInput(false)
                              }
                            }}
                          />
                          <Button
                            size="sm"
                            className={cn(optionTextSizeClass)} // Apply text size to button
                          >
                            <Check className={cn("h-4 w-4", optionTextSizeClass)} />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            className={cn(optionTextSizeClass)} // Apply text size to button
                          >
                            <X className={cn("h-4 w-4", optionTextSizeClass)} />
                          </Button>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}