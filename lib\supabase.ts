import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { enrichProductsWithRelatedData } from '@/lib/supabase-utils'

// Define the type for our client instance
type SupabaseClientType = ReturnType<
  typeof createClientComponentClient<Database>
>

// DEPRECATED: Use getSupabaseClient() instead to ensure proper authentication context
export const createSupabaseClient = () => {
  return createClientComponentClient<Database>()
}

// Export a function to get a client rather than a singleton instance
// This ensures we always get the current session
export const getSupabaseClient = () => {
  try {
    const client = createClientComponentClient<Database>({
      // Ensure session persistence across page refreshes
      cookieOptions: {
        name: 'supabase-auth-token',
        lifetime: 60 * 60 * 24 * 7, // 7 days
        domain: typeof window !== 'undefined' ? window.location.hostname : undefined,
        path: '/',
        sameSite: 'lax'
      }
    })

    // Add debug logging for session retrieval
    if (typeof window !== 'undefined') {
      console.debug('Supabase client created for URL:', window.location.href)
    }

    return client
  } catch (error) {
    console.error('Error creating Supabase client:', error)
    throw new Error('Failed to initialize Supabase client')
  }
}

// Cache for product data to prevent multiple simultaneous requests
const productCache = new Map<string, { data: any[]; timestamp: number }>()
const CACHE_DURATION = 5000 // 5 seconds

// Clean up old cache entries periodically
setInterval(() => {
  const now = Date.now()
  const keysToDelete: string[] = []

  // Collect keys to delete
  productCache.forEach((value, key) => {
    if (now - value.timestamp > CACHE_DURATION * 2) {
      keysToDelete.push(key)
    }
  })

  // Delete the collected keys
  keysToDelete.forEach((key) => {
    productCache.delete(key)
  })
}, 30000) // Clean up every 30 seconds

// Function to verify and refresh the schema cache
export async function verifyProductSchema() {
  const supabase = createSupabaseClient()
  try {
    // Attempt to query with batch_reference to verify column exists
    const { data, error } = await supabase
      .from('products')
      .select('batch_reference')
      .limit(1)

    if (
      error &&
      error.message.includes('column') &&
      error.message.includes('batch_reference')
    ) {
      return {
        valid: false,
        error: 'batch_reference column not found in schema',
      }
    }

    // Verify product_variants schema as well
    const { data: variantData, error: variantError } = await supabase
      .from('product_variants')
      .select('cost_adjustment')
      .limit(1)

    if (variantError && variantError.message.includes('column')) {
      return {
        valid: false,
        error:
          'Schema issue with product_variants table: ' + variantError.message,
      }
    }

    return { valid: true, error: null }
  } catch (error: unknown) {
    if (error instanceof Error) {
      // Check if it's a schema cache error
      if (
        error.message.includes('column') &&
        error.message.includes('not found')
      ) {
        return { valid: false, error: 'Schema cache issue detected' }
      }
      return { valid: false, error: error.message }
    }
    return { valid: false, error: 'Unknown error occurred' }
  }
}

// Function to refresh the Supabase schema cache
export async function refreshSchemaCache() {
  const supabase = createSupabaseClient()
  try {
    // Multiple approaches to refresh schema cache
    console.log('Refreshing schema cache...')

    // Approach 1: Query products table with basic columns
    console.log('Approach 1: Querying products table with basic columns...')
    const { data, error } = await supabase
      .from('products')
      .select('id, organization_id, name')
      .limit(1)

    if (error) {
      console.error('Error refreshing schema cache (attempt 1):', error)
    } else {
      console.log('First products query successful')
    }

    // Approach 2: Query notifications table
    console.log('Approach 2: Querying notifications table...')
    const { data: notificationData, error: notificationError } = await supabase
      .from('notifications')
      .select('id, organization_id, title')
      .limit(1)

    if (notificationError) {
      console.error('Error in notifications query:', notificationError)
    } else {
      console.log('Notifications query successful')
    }

    // Approach 3: Query product_variants table with necessary fields
    console.log(
      'Approach 3: Querying product_variants table with cost_adjustment...'
    )
    const { data: variantsData, error: variantsError } = await supabase
      .from('product_variants')
      .select('id, product_id, cost_adjustment, price')
      .limit(1)

    if (variantsError) {
      console.error('Error in product_variants query:', variantsError)
    } else {
      console.log('Product variants query successful')
    }

    // Approach 4: Try to query with batch_reference specifically
    console.log('Approach 4: Testing batch_reference column...')
    try {
      const { data: batchData, error: batchError } = await supabase
        .from('products')
        .select('batch_reference')
        .limit(1)

      if (batchError) {
        console.error('Error querying batch_reference:', batchError)
        if (batchError.message.includes('batch_reference')) {
          console.log(
            'This is expected if the schema cache needs time to refresh'
          )
        }
      } else {
        console.log('Batch reference query successful')
      }
    } catch (batchQueryError) {
      console.error('Error in batch reference query attempt:', batchQueryError)
    }

    console.log('Schema cache refresh attempt completed')
    return { success: true, error: null }
  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error('Error refreshing schema cache:', error)
      return { success: false, error: error.message }
    }
    console.error('Unknown error refreshing schema cache')
    return { success: false, error: 'Unknown error occurred' }
  }
}

// Custom function to execute SQL (requires proper setup in Supabase)
export async function execSQL(sql: string) {
  const supabase = createSupabaseClient()

  try {
    // Note: This requires a custom RPC function in Supabase
    // For now, this is commented out as it requires backend setup
    throw new Error('Custom SQL execution not implemented')
    // const { data, error } = await supabase.rpc('exec_sql', { sql })
    // return { data, error }
  } catch (err) {
    return { data: null, error: err }
  }
}

// Helper types for better TypeScript support
export type ProductRow = Database['public']['Tables']['products']['Row']
export type ProductInsert = Database['public']['Tables']['products']['Insert']
export type ProductUpdate = Database['public']['Tables']['products']['Update']

export type ProductVariantRow =
  Database['public']['Tables']['product_variants']['Row']
export type ProductVariantInsert =
  Database['public']['Tables']['product_variants']['Insert']
export type ProductVariantUpdate =
  Database['public']['Tables']['product_variants']['Update']

export type StockMovementRow =
  Database['public']['Tables']['stock_movements']['Row']
export type StockMovementInsert =
  Database['public']['Tables']['stock_movements']['Insert']
export type StockMovementUpdate =
  Database['public']['Tables']['stock_movements']['Update']

export type ExpenseRow = Database['public']['Tables']['expenses']['Row']
export type ExpenseInsert = Database['public']['Tables']['expenses']['Insert']
export type ExpenseUpdate = Database['public']['Tables']['expenses']['Update']

export type CategoryRow = Database['public']['Tables']['categories']['Row']
export type CategoryInsert =
  Database['public']['Tables']['categories']['Insert']
export type CategoryUpdate =
  Database['public']['Tables']['categories']['Update']

export type ProfileRow = Database['public']['Tables']['profiles']['Row']
export type ProfileInsert = Database['public']['Tables']['profiles']['Insert']
export type ProfileUpdate = Database['public']['Tables']['profiles']['Update']

// Product with variants type (for complex queries)
export type ProductWithVariants = ProductRow & {
  variants?: ProductVariantRow[]
}

// Database types (will be generated from Supabase)
export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          business_name: string | null
          contact_info: any | null
          subscription_tier: string
          currency: string
          avatar_url: string | null
          first_name: string | null
          last_name: string | null
          display_name: string | null
          job_title: string | null
          created_at: string
        }
        Insert: {
          id: string
          business_name?: string | null
          contact_info?: any | null
          subscription_tier?: string
          currency?: string
          avatar_url?: string | null
          first_name?: string | null
          last_name?: string | null
          display_name?: string | null
          job_title?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          business_name?: string | null
          contact_info?: any | null
          subscription_tier?: string
          currency?: string
          avatar_url?: string | null
          first_name?: string | null
          last_name?: string | null
          display_name?: string | null
          job_title?: string | null
          created_at?: string
        }
      }
      categories: {
        Row: {
          id: string
          organization_id: string
          name: string
          description: string | null
          created_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          name: string
          description?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          name?: string
          description?: string | null
          created_at?: string
        }
      }
      products: {
        Row: {
          id: string
          organization_id: string
          category_id: string | null
          name: string
          description: string | null
          brand: string | null
          supplier: string | null
          base_sku: string | null
          has_variants: boolean
          track_inventory: boolean
          is_active: boolean
          base_cost: number | null
          packaging_cost: number
          price: number | null
          size: string | null
          color: string | null
          stock_quantity: number
          low_stock_threshold: number
          barcode: string | null
          image_url: string | null
          sale_price: number | null
          sale_start_date: string | null
          sale_end_date: string | null
          batch_reference: string | null
          purchase_date: string | null
          notes: string | null
          total_cost: number | null
          effective_price: number | null
          is_on_sale: boolean | null
          profit_amount: number | null
          profit_margin: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          category_id?: string | null
          name: string
          description?: string | null
          brand?: string | null
          supplier?: string | null
          base_sku?: string | null
          has_variants?: boolean
          track_inventory?: boolean
          is_active?: boolean
          base_cost?: number | null
          packaging_cost?: number
          price?: number | null
          size?: string | null
          color?: string | null
          stock_quantity?: number
          low_stock_threshold?: number
          barcode?: string | null
          image_url?: string | null
          sale_price?: number | null
          sale_start_date?: string | null
          sale_end_date?: string | null
          batch_reference?: string | null
          purchase_date?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          category_id?: string | null
          name?: string
          description?: string | null
          brand?: string | null
          supplier?: string | null
          base_sku?: string | null
          has_variants?: boolean
          track_inventory?: boolean
          is_active?: boolean
          base_cost?: number | null
          packaging_cost?: number
          price?: number | null
          size?: string | null
          color?: string | null
          stock_quantity?: number
          low_stock_threshold?: number
          barcode?: string | null
          image_url?: string | null
          sale_price?: number | null
          sale_start_date?: string | null
          sale_end_date?: string | null
          batch_reference?: string | null
          purchase_date?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      product_variants: {
        Row: {
          id: string
          product_id: string
          organization_id: string
          sku: string
          variant_name: string | null
          size: string | null
          color: string | null
          material: string | null
          style: string | null
          weight: number | null
          dimensions: any | null
          cost_adjustment: number
          base_cost: number | null
          price: number
          sale_price: number | null
          sale_start_date: string | null
          sale_end_date: string | null
          stock_quantity: number
          low_stock_threshold: number
          reserved_quantity: number
          is_active: boolean
          barcode: string | null
          image_urls: string[] | null
          effective_price: number | null
          is_on_sale: boolean | null
          available_quantity: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          product_id: string
          organization_id: string
          sku: string
          variant_name?: string | null
          size?: string | null
          color?: string | null
          material?: string | null
          style?: string | null
          weight?: number | null
          dimensions?: any | null
          cost_adjustment?: number
          base_cost?: number | null
          price: number
          sale_price?: number | null
          sale_start_date?: string | null
          sale_end_date?: string | null
          stock_quantity?: number
          low_stock_threshold?: number
          reserved_quantity?: number
          is_active?: boolean
          barcode?: string | null
          image_urls?: string[] | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          product_id?: string
          organization_id?: string
          sku?: string
          variant_name?: string | null
          size?: string | null
          color?: string | null
          material?: string | null
          style?: string | null
          weight?: number | null
          dimensions?: any | null
          cost_adjustment?: number
          base_cost?: number | null
          price?: number
          sale_price?: number | null
          sale_start_date?: string | null
          sale_end_date?: string | null
          stock_quantity?: number
          low_stock_threshold?: number
          reserved_quantity?: number
          is_active?: boolean
          barcode?: string | null
          image_urls?: string[] | null
          created_at?: string
          updated_at?: string
        }
      }
      stock_movements: {
        Row: {
          id: string
          organization_id: string
          product_id: string | null
          variant_id: string | null
          movement_type:
            | 'initial_stock'
            | 'purchase'
            | 'sale'
            | 'adjustment'
            | 'transfer'
            | 'damage'
            | 'return'
            | 'reserve'
            | 'unreserve'
          quantity_change: number
          previous_quantity: number
          new_quantity: number
          reason: string | null
          reference_id: string | null
          unit_cost: number | null
          notes: string | null
          created_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          product_id?: string | null
          variant_id?: string | null
          movement_type:
            | 'initial_stock'
            | 'purchase'
            | 'sale'
            | 'adjustment'
            | 'transfer'
            | 'damage'
            | 'return'
            | 'reserve'
            | 'unreserve'
          quantity_change: number
          previous_quantity: number
          new_quantity: number
          reason?: string | null
          reference_id?: string | null
          unit_cost?: number | null
          notes?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          product_id?: string | null
          variant_id?: string | null
          movement_type?:
            | 'initial_stock'
            | 'purchase'
            | 'sale'
            | 'adjustment'
            | 'transfer'
            | 'damage'
            | 'return'
            | 'reserve'
            | 'unreserve'
          quantity_change?: number
          previous_quantity?: number
          new_quantity?: number
          reason?: string | null
          reference_id?: string | null
          unit_cost?: number | null
          notes?: string | null
          created_at?: string
        }
      }
      expenses: {
        Row: {
          id: string
          organization_id: string
          expense_id: string | null
          category: string
          amount: number
          description: string | null
          vendor: string | null
          payment_method: string | null
          receipt_url: string | null
          expense_date: string
          created_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          expense_id?: string | null
          category: string
          amount: number
          description?: string | null
          vendor?: string | null
          payment_method?: string | null
          receipt_url?: string | null
          expense_date: string
          created_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          expense_id?: string | null
          category?: string
          amount?: number
          description?: string | null
          vendor?: string | null
          payment_method?: string | null
          receipt_url?: string | null
          expense_date?: string
          created_at?: string
        }
      }
      product_attributes: {
        Row: {
          id: string
          organization_id: string
          name: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          name: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          name?: string
          created_at?: string
          updated_at?: string
        }
      }
      product_attribute_values: {
        Row: {
          id: string
          organization_id: string
          attribute_id: string
          value: string
          created_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          attribute_id: string
          value: string
          created_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          attribute_id?: string
          value?: string
          created_at?: string
        }
      }
      notifications: {
        Row: {
          id: string
          organization_id: string
          type: string
          title: string
          message: string
          related_entity_type: string | null
          related_entity_id: string | null
          is_read: boolean
          created_at: string
          read_at: string | null
        }
        Insert: {
          id?: string
          organization_id: string
          type: string
          title: string
          message: string
          related_entity_type?: string | null
          related_entity_id?: string | null
          is_read?: boolean
          created_at?: string
          read_at?: string | null
        }
        Update: {
          id?: string
          organization_id?: string
          type?: string
          title?: string
          message?: string
          related_entity_type?: string | null
          related_entity_id?: string | null
          is_read?: boolean
          created_at?: string
          read_at?: string | null
        }
      }
      organizations: {
        Row: {
          id: string
          name: string
          owner_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          owner_id: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          owner_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      organization_members: {
        Row: {
          id: string
          organization_id: string
          user_id: string
          role: string
          created_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          user_id: string
          role: string
          created_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          user_id?: string
          role?: string
          created_at?: string
        }
      }
      organization_preferences: {
        Row: {
          id: string
          organization_id: string
          email_notifications: boolean
          push_notifications: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          organization_id: string
          email_notifications?: boolean
          push_notifications?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          organization_id?: string
          email_notifications?: boolean
          push_notifications?: boolean
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}

// Product Attributes Functions
export async function getProductAttributes(organizationId: string) {
  const supabase = getSupabaseClient()

  try {
    const { data, error } = await supabase
      .from('product_attributes')
      .select('*')
      .eq('organization_id', organizationId)
      .order('name')

    if (error) {
      console.error('Error fetching product attributes:', error)
      throw error
    }

    return data
  } catch (error) {
    console.error('Error in getProductAttributes:', error)
    throw error
  }
}

export async function createProductAttribute(
  organizationId: string,
  name: string
) {
  const supabase = getSupabaseClient()

  try {
    const { data, error } = await supabase
      .from('product_attributes')
      .insert({ organization_id: organizationId, name })
      .select()
      .single()

    if (error) {
      console.error('Error creating product attribute:', error)
      throw error
    }

    return data
  } catch (error) {
    console.error('Error in createProductAttribute:', error)
    throw error
  }
}

// New functions for handling attribute values
export async function getProductAttributeValues(
  organizationId: string,
  attributeId: string
) {
  const supabase = getSupabaseClient()

  try {
    const { data, error } = await supabase
      .from('product_attribute_values')
      .select('value')
      .eq('organization_id', organizationId)
      .eq('attribute_id', attributeId)
      .order('value')

    if (error) {
      console.error('Error fetching product attribute values:', error)
      throw error
    }

    return data.map((item) => ({ value: item.value, label: item.value }))
  } catch (error) {
    console.error('Error in getProductAttributeValues:', error)
    throw error
  }
}

export async function createProductAttributeValue(
  organizationId: string,
  attributeId: string,
  value: string
) {
  const supabase = getSupabaseClient()

  try {
    const { data, error } = await supabase
      .from('product_attribute_values')
      .insert({
        organization_id: organizationId,
        attribute_id: attributeId,
        value,
      })
      .select()
      .single()

    if (error) {
      // If it's a duplicate value error, we can ignore it
      if (error.code === '23505') {
        // Unique violation
        // Only log in development mode
        if (process.env.NODE_ENV === 'development') {
          console.log('Attribute value already exists:', value)
        }
        return null
      }
      console.error('Error creating product attribute value:', error)
      throw error
    }

    return data
  } catch (error) {
    console.error('Error in createProductAttributeValue:', error)
    throw error
  }
}

// Function to get all expenses for an organization
export async function getAllExpensesForOrganization(organizationId: string) {
  // Create a fresh client instance for each request to avoid connection issues
  const supabase = getSupabaseClient()

  console.log('Fetching all expenses for organization:', organizationId)

  try {
    const { data, error } = await supabase
      .from('expenses')
      .select('*')
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching all expenses for organization:', error)
      throw error
    }

    console.log(
      'Successfully fetched expenses for organization:',
      organizationId,
      'Count:',
      data?.length || 0
    )

    return data
  } catch (error) {
    console.error('Error in getAllExpensesForOrganization:', error)
    throw error
  }
}

// Function to get low stock items for notifications
export async function getLowStockItemsForOrganization(organizationId: string) {
  // Create a fresh client instance for each request to avoid connection issues
  const supabase = getSupabaseClient()

  try {
    const { data, error } = await supabase
      .from('products')
      .select(
        `
        id,
        name,
        base_sku,
        stock_quantity,
        low_stock_threshold,
        has_variants,
        product_variants(
          id,
          variant_name,
          sku,
          stock_quantity,
          low_stock_threshold
        )
      `
      )
      .eq('organization_id', organizationId)
      .eq('is_active', true)

    if (error) {
      console.error('Error fetching low stock items for organization:', error)
      throw error
    }

    return data
  } catch (error) {
    console.error('Error in getLowStockItemsForOrganization:', error)
    throw error
  }
}

// Function to get table columns
export async function getTableColumns(tableName: string) {
  // Create a fresh client instance for each request to avoid connection issues
  const supabase = getSupabaseClient()

  try {
    // Query the information_schema to get column information
    const { data, error } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable, column_default')
      .eq('table_name', tableName)
      .eq('table_schema', 'public')
      .order('ordinal_position')

    if (error) {
      console.error('Error getting table columns:', error)
      throw error
    }

    return data
  } catch (error) {
    console.error('Error in getTableColumns:', error)
    throw error
  }
}

// Function to verify if a specific column exists in a table
export async function verifyColumnExists(
  tableName: string,
  columnName: string
) {
  // Create a fresh client instance for each request to avoid connection issues
  const supabase = getSupabaseClient()

  try {
    // Try to query the actual table with just that column
    try {
      const { data: testData, error: testError } = await supabase
        .from(tableName)
        .select(columnName)
        .limit(1)

      if (testError && testError.message.includes('not found')) {
        // Column definitely doesn't exist or schema cache issue
        return { success: true, exists: false, error: testError }
      } else if (testError) {
        // Some other error
        console.error('Error in direct table query:', testError)
        throw testError
      } else {
        // Query succeeded, column exists
        return { success: true, exists: true, data: testData }
      }
    } catch (directError) {
      console.error('Error in direct table query attempt:', directError)
      throw directError
    }
  } catch (error) {
    console.error('Error in verifyColumnExists:', error)
    throw error
  }
}

// Function to list all tables in the public schema
export async function listAllTables() {
  // Create a fresh client instance for each request to avoid connection issues
  const supabase = getSupabaseClient()

  try {
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name, table_type')
      .eq('table_schema', 'public')
      .order('table_name')

    if (error) {
      console.error('Error listing tables:', error)
      throw error
    }

    return data
  } catch (error) {
    console.error('Error in listAllTables:', error)
    throw error
  }
}

// Function to get detailed information about a specific table
export async function getTableInfo(tableName: string) {
  // Create a fresh client instance for each request to avoid connection issues
  const supabase = getSupabaseClient()

  try {
    const { data, error } = await supabase
      .from('information_schema.columns')
      .select(
        `
        column_name,
        data_type,
        is_nullable,
        column_default,
        is_primary_key: constraint_column_usage(constraint_name)
      `
      )
      .eq('table_name', tableName)
      .eq('table_schema', 'public')
      .order('ordinal_position')

    if (error) {
      console.error(`Error getting info for table ${tableName}:`, error)
      throw error
    }

    return { tableName, data }
  } catch (error) {
    console.error(`Error in getTableInfo for ${tableName}:`, error)
    throw error
  }
}

// Function to find all tables with 'product' in their name
export async function findProductTables() {
  // Create a fresh client instance for each request to avoid connection issues
  const supabase = getSupabaseClient()

  try {
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .ilike('table_name', '%product%')

    if (error) {
      console.error('Error finding product tables:', error)
      throw error
    }

    // Get detailed info for each product table
    const tableDetails = []
    for (const table of data) {
      const tableInfo = await getTableInfo(table.table_name)
      tableDetails.push({
        tableName: table.table_name,
        columns: tableInfo.data,
      })
    }

    return tableDetails
  } catch (error) {
    console.error('Error in findProductTables:', error)
    throw error
  }
}

// Function to force refresh the Supabase schema cache
export async function forceRefreshSchema() {
  // Create a fresh client instance for each request to avoid connection issues
  const supabase = getSupabaseClient()

  try {
    console.log('Force refreshing Supabase schema cache...')

    // Method 1: Query the actual products table with specific columns
    const { data: productData, error: productError } = await supabase
      .from('products')
      .select('id, organization_id, name')
      .limit(1)

    if (productError) {
      console.error('Error in products table query:', productError)
    } else {
      console.log('Products table query successful')
    }

    // Method 2: Query the notifications table
    const { data: notificationData, error: notificationError } = await supabase
      .from('notifications')
      .select('id, organization_id, title')
      .limit(1)

    if (notificationError) {
      console.error('Error in notifications table query:', notificationError)
    } else {
      console.log('Notifications table query successful')
    }

    // Method 3: Try to query with batch_reference specifically
    try {
      const { data: batchData, error: batchError } = await supabase
        .from('products')
        .select('batch_reference')
        .limit(1)

      if (batchError) {
        console.error('Error querying batch_reference:', batchError)
        // This might be expected if the column was recently added
      } else {
        console.log('Batch reference query successful')
      }
    } catch (batchQueryError) {
      console.error('Error in batch reference query attempt:', batchQueryError)
    }

    // Method 4: Query stock_movements table to ensure it's in schema cache
    try {
      const { data: stockData, error: stockError } = await supabase
        .from('stock_movements')
        .select('id')
        .limit(1)

      if (stockError) {
        console.error('Error querying stock_movements:', stockError)
        // This might be the schema cache issue we're trying to fix
      } else {
        console.log('Stock movements query successful')
      }
    } catch (stockQueryError) {
      console.error('Error in stock movements query attempt:', stockQueryError)
    }

    // Method 5: Query product_variants table with cost_adjustment
    try {
      const { data: variantsData, error: variantsError } = await supabase
        .from('product_variants')
        .select('id, product_id, cost_adjustment')
        .limit(1)

      if (variantsError) {
        console.error('Error querying product_variants:', variantsError)
        // This might be the schema cache issue we're trying to fix
      } else {
        console.log('Product variants query successful')
      }
    } catch (variantsQueryError) {
      console.error(
        'Error in product variants query attempt:',
        variantsQueryError
      )
    }

    console.log('Schema refresh completed')
    return { success: true }
  } catch (error) {
    console.error('Error in forceRefreshSchema:', error)
    throw error
  }
}

// Function to get all products for an organization with their variants and categories (including drafts)
export async function getAllProductsForOrganization(organizationId: string) {
  // Create a fresh client instance for each request to avoid connection issues
  const supabase = getSupabaseClient()

  try {
    console.log('Fetching all products for organization:', organizationId)
    const { data, error } = await supabase
      .from('products')
      .select(`
        id,
        organization_id,
        category_id,
        name,
        description,
        brand,
        supplier,
        base_sku,
        has_variants,
        track_inventory,
        is_active,
        base_cost,
        packaging_cost,
        price,
        size,
        color,
        stock_quantity,
        low_stock_threshold,
        barcode,
        image_url,
        sale_price,
        sale_start_date,
        sale_end_date,
        batch_reference,
        purchase_date,
        notes,
        total_cost,
        effective_price,
        is_on_sale,
        profit_amount,
        profit_margin,
        created_at,
        updated_at
      `)
      .eq('organization_id', organizationId)

    if (error) {
      console.error('Error fetching all products for organization:', error)
      throw error
    }

    // Enrich products with category names and variants using our utility function
    const enrichedProducts = await enrichProductsWithRelatedData(data)

    console.log(
      'Successfully fetched products for organization:',
      organizationId,
      'Count:',
      enrichedProducts?.length
    )
    return enrichedProducts
  } catch (error) {
    console.error('Error in getAllProductsForOrganization:', error)
    throw error
  }
}

// Function to get all products for an organization with their variants and categories (only active products)
export async function getProductsForOrganization(organizationId: string) {
  const supabase = getSupabaseClient()

  try {
    console.log('Fetching active products for organization:', organizationId)
    const { data, error } = await supabase
      .from('products')
      .select(`
        id,
        organization_id,
        category_id,
        name,
        description,
        brand,
        supplier,
        base_sku,
        has_variants,
        track_inventory,
        is_active,
        base_cost,
        packaging_cost,
        price,
        size,
        color,
        stock_quantity,
        low_stock_threshold,
        barcode,
        image_url,
        sale_price,
        sale_start_date,
        sale_end_date,
        batch_reference,
        purchase_date,
        notes,
        total_cost,
        effective_price,
        is_on_sale,
        profit_amount,
        profit_margin,
        created_at,
        updated_at
      `)
      .eq('organization_id', organizationId)
      .eq('is_active', true)

    if (error) {
      console.error('Error fetching products for organization:', error)
      throw error
    }

    // Enrich products with category names and variants using our utility function
    const enrichedProducts = await enrichProductsWithRelatedData(data)

    console.log(
      'Successfully fetched active products for organization:',
      organizationId,
      'Count:',
      enrichedProducts?.length
    )
    return enrichedProducts
  } catch (error) {
    console.error('Error in getProductsForOrganization:', error)
    throw error
  }
}

// Function to get best-selling items (top 5 by quantity sold in last 30 days)
export async function getBestSellingItems(
  organizationId: string,
  limit: number = 5,
  dateRange: '30d' | '90d' | '1y' | 'all' = '30d'
) {
  const supabase = getSupabaseClient()

  try {
    // Calculate date based on range
    const now = new Date()
    let startDate: Date | null = null
    
    switch (dateRange) {
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
        break
      case 'all':
        // For all time, we don't set a start date filter
        startDate = null
        break
    }

    // Query to get top selling products/variants by quantity sold
    let query = supabase
      .from('stock_movements')
      .select(
        `
        product_id,
        variant_id,
        quantity_change
      `
      )
      .eq('organization_id', organizationId)
      .eq('movement_type', 'sale')
      
    // Add date filter if not 'all'
    if (startDate) {
      query = query.gte('created_at', startDate.toISOString())
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching best selling items:', error)
      throw error
    }

    // Extract product and variant IDs
    const productIds = [...new Set(data.map(item => item.product_id).filter(Boolean))] as string[]
    const variantIds = [...new Set(data.map(item => item.variant_id).filter(Boolean))] as string[]

    // Fetch product data
    let productMap: Record<string, any> = {}
    if (productIds.length > 0) {
      const { data: products, error: productError } = await supabase
        .from('products')
        .select('id, name, base_sku')
        .in('id', productIds)
      
      if (productError) {
        console.error('Error fetching products:', productError)
        throw productError
      }
      
      productMap = products.reduce((map, product) => {
        map[product.id] = product
        return map
      }, {} as Record<string, any>)
    }

    // Fetch variant data
    let variantMap: Record<string, any> = {}
    if (variantIds.length > 0) {
      const { data: variants, error: variantError } = await supabase
        .from('product_variants')
        .select('id, product_id, variant_name, sku')
        .in('id', variantIds)
      
      if (variantError) {
        console.error('Error fetching variants:', variantError)
        throw variantError
      }
      
      variantMap = variants.reduce((map, variant) => {
        map[variant.id] = variant
        return map
      }, {} as Record<string, any>)
    }

    // Process data to aggregate by product/variant
    const salesAggregation: Record<string, number> = {}

    // Aggregate sales data by summing quantities for each product/variant
    data.forEach((item) => {
      const key = item.variant_id || item.product_id
      
      // Skip if we don't have a valid key
      if (!key) return
      
      // Add the absolute value of quantity_change (negative for sales)
      if (!salesAggregation[key]) {
        salesAggregation[key] = 0
      }
      salesAggregation[key] += Math.abs(item.quantity_change)
    })

    // Convert aggregated data to result format
    const result: {
      id: string
      name: string
      sku: string
      quantitySold: number
      isVariant: boolean
    }[] = []

    // Process each aggregated item
    Object.entries(salesAggregation).forEach(([key, quantitySold]) => {
      let name = 'Unknown Product'
      let sku = 'N/A'
      let isVariant = false

      // Determine if this is a variant or product and get the appropriate data
      if (variantMap[key]) {
        // This is a variant
        const variant = variantMap[key]
        const product = productMap[variant.product_id]
        name = `${product?.name || 'Unknown Product'} - ${variant.variant_name || 'Unnamed Variant'}`
        sku = variant.sku || 'N/A'
        isVariant = true
      } else if (productMap[key]) {
        // This is a product
        const product = productMap[key]
        name = product.name || 'Unknown Product'
        sku = product.base_sku || 'N/A'
        isVariant = false
      }

      result.push({
        id: key,
        name,
        sku,
        quantitySold,
        isVariant
      })
    })

    // Sort by quantity sold (descending) and limit results
    result.sort((a, b) => b.quantitySold - a.quantitySold)
    return result.slice(0, limit)
  } catch (error) {
    console.error('Error in getBestSellingItems:', error)
    throw error
  }
}

// Function to get slow-moving inventory count (items with stock but zero sales in last 90 days)
export async function getSlowMovingInventoryCount(organizationId: string) {
  const supabase = getSupabaseClient()

  try {
    // Calculate date 90 days ago
    const ninetyDaysAgo = new Date()
    ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90)
    const ninetyDaysAgoString = ninetyDaysAgo.toISOString()

    // First, get all products and variants with stock quantity > 0
    const { data: productsWithStock, error: productsError } = await supabase
      .from('products')
      .select(
        `
        id,
        name,
        base_sku,
        stock_quantity,
        product_variants (
          id,
          variant_name,
          sku,
          stock_quantity
        )
      `
      )
      .eq('organization_id', organizationId)
      .gt('stock_quantity', 0)

    if (productsError) {
      console.error('Error fetching products with stock:', productsError)
      throw productsError
    }

    // Get all product and variant IDs with stock
    const productIdsWithStock = productsWithStock.map((p) => p.id)
    const variantIdsWithStock: string[] = []

    productsWithStock.forEach((product) => {
      if (product.product_variants && product.product_variants.length > 0) {
        product.product_variants.forEach((variant) => {
          if (variant.stock_quantity && variant.stock_quantity > 0) {
            variantIdsWithStock.push(variant.id)
          }
        })
      }
    })

    // Get all IDs (products and variants) with stock
    const allIdsWithStock = [...productIdsWithStock, ...variantIdsWithStock]

    // Now get all sales in the last 90 days
    const { data: recentSales, error: salesError } = await supabase
      .from('stock_movements')
      .select('product_id, variant_id')
      .eq('organization_id', organizationId)
      .eq('movement_type', 'sale')
      .gte('created_at', ninetyDaysAgoString)

    if (salesError) {
      console.error('Error fetching recent sales:', salesError)
      throw salesError
    }

    // Get IDs of products/variants that had sales
    const productIdsWithSales = new Set(
      recentSales.map((s) => s.product_id).filter(Boolean)
    )
    const variantIdsWithSales = new Set(
      recentSales.map((s) => s.variant_id).filter(Boolean)
    )

    // Count items with stock but no sales
    let slowMovingCount = 0

    // Check simple products
    productIdsWithStock.forEach((productId) => {
      if (!productIdsWithSales.has(productId)) {
        slowMovingCount++
      }
    })

    // Check variants
    variantIdsWithStock.forEach((variantId) => {
      if (!variantIdsWithSales.has(variantId)) {
        slowMovingCount++
      }
    })

    return slowMovingCount
  } catch (error) {
    console.error('Error in getSlowMovingInventoryCount:', error)
    throw error
  }
}
