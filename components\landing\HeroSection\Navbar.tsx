"use client";

import Link from "next/link";

const Navbar = () => {
  return (
    <nav className="flex items-center justify-between py-6">
      <div className="flex items-center">
        {/* Logo - replace with actual logo */}
        <div className="text-white font-bold text-xl">ONKO</div>
      </div>
      
      <div className="hidden md:flex items-center space-x-8">
        <a href="#" className="text-slate-300 hover:text-white transition-colors">Features</a>
        <a href="#" className="text-slate-300 hover:text-white transition-colors">Pricing</a>
        <a href="#" className="text-slate-300 hover:text-white transition-colors">About Us</a>
      </div>
      
      <div className="flex items-center space-x-4">
        <Link href="/auth/signin">
          <button className="px-4 py-2 border border-slate-700 text-slate-300 rounded-lg hover:bg-slate-800 transition-colors">
            Sign In
          </button>
        </Link>
        <Link href="/auth/signup">
          <button className="px-4 py-2 bg-white text-slate-900 rounded-lg font-medium hover:bg-slate-100 transition-colors">
            Start Free Trial
          </button>
        </Link>
      </div>
    </nav>
  );
};

export default Navbar;