'use client'

import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ArrowRight, Package, Layers } from 'lucide-react'
import { useProductForm } from '../../../context/ProductFormContext'
import { designSystem, getCardClasses, getBadgeClasses, getButtonClasses } from '../../../config/design-system'
import { SimplePricingForm } from './SimplePricingForm'
import { SalePricingForm } from './SalePricingForm'
import { AttributeManager } from './AttributeManager'
import { VariantGenerator } from './VariantGenerator'
import { VariantConfigurator } from './VariantConfigurator'

export function PricingVariantsSection() {
  const { formData, updateFormData } = useProductForm()

  const upgradeToVariable = () => {
    updateFormData('has_variants', true)
  }

  const downgradeToSimple = () => {
    updateFormData('has_variants', false)
    // Clear variant data
    updateFormData('variant_attributes', [])
  }

  return (
    <div className="space-y-4">
      {/* Content based on product type */}
      {!formData.has_variants ? (
        /* Simple Product Pricing */
        <>
          <Card className={getCardClasses()}>
            <CardContent className="p-4">
              <SimplePricingForm />
            </CardContent>
          </Card>
          
          {/* Sale Pricing without extra container for consistent spacing */}
          <SalePricingForm />
        </>
      ) : (
        /* Variable Product Management */
        <div className="space-y-4">
          {/* Attribute Management */}
          <Card className={getCardClasses()}>
            <CardContent className="p-4">
              <AttributeManager />
            </CardContent>
          </Card>

          {/* Variant Generation */}
          <Card className={getCardClasses()}>
            <CardContent className="p-4">
              <VariantGenerator />
            </CardContent>
          </Card>

          {/* Variant Configuration */}
          <Card className={getCardClasses()}>
            <CardContent className="p-4">
              <VariantConfigurator />
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}