'use client'

import React, { useState } from 'react'
import { Input } from '@/components/ui/input'
import { DatePicker } from '@/components/ui/date-picker'
import { ChevronDown, ChevronRight } from 'lucide-react'
import { useProductForm } from '../../../context/ProductFormContext'
import { designSystem, getInputClasses } from '../../../config/design-system'
import { FormField } from '../../shared/FormField'

export function SalePricingForm() {
  const { formData, updateFormData, errors } = useProductForm()
  const [isExpanded, setIsExpanded] = useState(false)

  return (
    <div className="border border-gray-200 rounded-lg">
      <button
        type="button"
        className="w-full flex items-center justify-between p-4 text-left"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div>
          <h3 className="text-sm font-medium text-gray-700">
            Sale Settings
          </h3>
        </div>
        {isExpanded ? (
          <ChevronDown className="h-5 w-5 text-gray-500" />
        ) : (
          <ChevronRight className="h-5 w-5 text-gray-500" />
        )}
      </button>
      
      {isExpanded && (
        <div className="border-t border-gray-200 p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FormField
              label="Sale Price"
              error={errors.sale_price}
            >
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-xs">$</span>
                <Input
                  type="number"
                  value={formData.sale_price || ''}
                  onChange={(e) => updateFormData('sale_price', parseFloat(e.target.value) || null)}
                  placeholder="0.00"
                  className={`${getInputClasses(!!errors.sale_price)} pl-6`}
                  step="0.01"
                  min="0"
                />
              </div>
            </FormField>

            <FormField
              label="Sale Start Date"
              error={errors.sale_start_date}
            >
              <DatePicker
                date={formData.sale_start_date || undefined}
                onDateChange={(date) => updateFormData('sale_start_date', date || null)}
                placeholder="Select start date"
                className={getInputClasses(!!errors.sale_start_date)}
              />
            </FormField>

            <FormField
              label="Sale End Date"
              error={errors.sale_end_date}
            >
              <DatePicker
                date={formData.sale_end_date || undefined}
                onDateChange={(date) => updateFormData('sale_end_date', date || null)}
                placeholder="Select end date"
                className={getInputClasses(!!errors.sale_end_date)}
              />
            </FormField>
          </div>

          {formData.sale_price && formData.price && (
            <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
              <p className={`${designSystem.typography.caption} text-green-700`}>
                <strong>Sale Discount:</strong> {(((formData.price - formData.sale_price) / formData.price) * 100).toFixed(1)}% off
                (Save ${(formData.price - formData.sale_price).toFixed(2)})
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}