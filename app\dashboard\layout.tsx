'use client'

import { useAuth } from '@/contexts/auth-context'
import { Sidebar } from '@/components/sidebar'
import { Header } from '@/components/header'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { ErrorBoundary } from '@/components/ui/error-boundary'
// Removed import of useTabFocusRefresher hook

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [showLoading, setShowLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Removed initialization of useTabFocusRefresher hook
  
  // Redirect to sign-in if not authenticated and not loading
  useEffect(() => {
    if (!loading && !user) {
      router.replace('/auth/signin')
    }
  }, [user, loading, router])

  // Handle loading timeout to prevent indefinite skeleton display
  useEffect(() => {
    if (loading) {
      setShowLoading(true)
      const timer = setTimeout(() => {
        setShowLoading(false)
        setError('Dashboard is taking longer than expected to load. Please refresh the page.')
      }, 8000) // Show loading for maximum 8 seconds
      
      return () => clearTimeout(timer)
    }
  }, [loading])

  // Handle network status changes
  useEffect(() => {
    const handleOnline = () => {
      if (error) {
        setError(null)
      }
    }

    const handleOffline = () => {
      setError('You are currently offline. Please check your internet connection.')
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)
    
    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [error])

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center p-6 max-w-md">
          <div className="text-2xl font-bold text-red-600 mb-2">Dashboard Error</div>
          <p className="text-gray-700 mb-4">
            {error}
          </p>
          <div className="flex flex-col gap-2">
            <button 
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
              onClick={() => window.location.reload()}
            >
              Reload Page
            </button>
            <button 
              className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
              onClick={() => router.replace('/auth/signin')}
            >
              Go to Sign In
            </button>
          </div>
        </div>
      </div>
    )
  }

  if (loading && showLoading) {
    console.log('DashboardLayout: Showing loading state')
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center gap-2">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading your dashboard...</p>
          <p className="text-sm text-gray-500">Please wait while we set up your workspace</p>
        </div>
      </div>
    )
  }

  // If loading timed out but we still don't have user data, show error state
  if (loading && !showLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center p-6 max-w-md">
          <div className="text-2xl font-bold text-red-600 mb-2">Connection Issue</div>
          <p className="text-gray-700 mb-4">
            We're having trouble connecting to the server. Please check your internet connection.
          </p>
          <button 
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            onClick={() => window.location.reload()}
          >
            Reload Page
          </button>
        </div>
      </div>
    )
  }

  if (!user) {
    console.log('DashboardLayout: Showing redirecting state')
    return null // Return null instead of a loading message to prevent UI flicker
  }

  console.log('DashboardLayout: Rendering dashboard')
  return (
    <ErrorBoundary>
      <div className="flex h-screen bg-white">
        <Sidebar />
        <div className="flex-1 flex flex-col">
          <Header />
          <main className="flex-1 overflow-y-auto pt-14"> {/* Add top padding for header */}
            <div className="p-4 md:p-6 -mt-3">
              {children}
            </div>
          </main>
        </div>
      </div>
    </ErrorBoundary>
  )
}