'use client'

import React from 'react'
import { useProductForm } from '../../../context/ProductFormContext'
import { designSystem } from '../../../config/design-system'
import { FormField } from '../../shared/FormField'
import { SimpleDropdown } from '@/components/ui/simple-dropdown'

interface ProductTypeOption {
  id: 'simple' | 'variable'
  title: string
  description: string
}

const PRODUCT_TYPES: ProductTypeOption[] = [
  {
    id: 'simple',
    title: 'Simple Product',
    description: 'A single product with no variations'
  },
  {
    id: 'variable',
    title: 'Variable Product',
    description: 'Product with multiple variations'
  }
]

export function ProductTypeSelector() {
  const { formData, updateFormData } = useProductForm()

  const handleTypeSelect = (value: string) => {
    updateFormData('has_variants', value === 'variable')
  }

  const selectedType = formData.has_variants ? 'variable' : 'simple'

  // Convert to dropdown options format
  const dropdownOptions = PRODUCT_TYPES.map(type => ({
    value: type.id,
    label: type.title
  }))

  return (
    // Reduced spacing from space-y-4 to space-y-2 for a more compact layout
    // Reduced padding from py-1 to py-0.5 for consistent padding when not in a card
    <div className="space-y-2 py-0">
      <FormField label="Product Type">
        <SimpleDropdown
          options={dropdownOptions}
          value={selectedType}
          onValueChange={handleTypeSelect}
          placeholder="Select product type"
          size="xs"
          textsize="xs"
          allowCustom={false}
          showSearch={false}
        />
      </FormField>
    </div>
  )
}