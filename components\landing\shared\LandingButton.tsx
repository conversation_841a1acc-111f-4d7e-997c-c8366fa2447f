"use client";

import { ButtonHTMLAttributes } from "react";
import Link, { LinkProps } from "next/link";

interface LandingButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary";
  href?: string;
}

const LandingButton = ({ 
  variant = "primary", 
  children, 
  className = "",
  href,
  ...props 
}: LandingButtonProps) => {
  const baseClasses = "px-6 py-3 font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-900";
  
  const variantClasses = variant === "primary" 
    ? "bg-white text-slate-900 hover:bg-slate-100 focus:ring-white" 
    : "border border-slate-700 text-slate-300 hover:bg-slate-800 focus:ring-slate-500";
  
  if (href) {
    return (
      <Link 
        href={href}
        className={`${baseClasses} ${variantClasses} ${className} inline-block text-center`}
      >
        {children}
      </Link>
    );
  }
  
  return (
    <button 
      className={`${baseClasses} ${variantClasses} ${className}`}
      {...props}
    >
      {children}
    </button>
  );
};

export default LandingButton;