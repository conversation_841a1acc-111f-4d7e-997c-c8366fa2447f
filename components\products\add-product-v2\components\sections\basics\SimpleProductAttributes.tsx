'use client'

import React, { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { SimpleDropdown } from '@/components/ui/simple-dropdown'
import { Plus, X, Tag } from 'lucide-react'
import { useProductForm } from '../../../context/ProductFormContext'
import { designSystem, getInputClasses } from '../../../config/design-system'
import { FormField } from '../../shared/FormField'
import { useProductAttributes } from '@/hooks/use-product-attributes'

interface SimpleAttribute {
  name: string
  value: string
}

export function SimpleProductAttributes() {
  const { formData, updateFormData, errors } = useProductForm()
  const { attributes, getAttributeValues, fetchAttributeValues, createAttribute, createAttributeValue } = useProductAttributes()
  const [newAttributeName, setNewAttributeName] = useState('')
  const [newAttributeValue, setNewAttributeValue] = useState('')
  const [isCreatingAttribute, setIsCreatingAttribute] = useState(false)
  const [isCreatingValue, setIsCreatingValue] = useState(false)
  const [attributeError, setAttributeError] = useState<string | null>(null)

  // Convert database attributes to dropdown options
  const attributeOptions = attributes.map(attr => ({
    value: attr.name.toLowerCase(),
    label: attr.name
  }))

  // When attribute selection changes, fetch its values
  useEffect(() => {
    if (newAttributeName) {
      // Find the attribute ID by name
      const selectedAttribute = attributes.find(attr => 
        attr.name.toLowerCase() === newAttributeName.toLowerCase()
      )
      
      if (selectedAttribute) {
        // Fetch values for this attribute
        fetchAttributeValues(selectedAttribute.id, newAttributeName)
      }
    }
  }, [newAttributeName, attributes, fetchAttributeValues])

  const addAttribute = async () => {
    if (!newAttributeName || !newAttributeValue) return

    try {
      setAttributeError(null)
      
      // Check if this attribute-value combination already exists
      const attributeExists = formData.custom_attributes.some(attr => 
        attr.name.toLowerCase() === newAttributeName.toLowerCase() && 
        attr.value.toLowerCase() === newAttributeValue.toLowerCase()
      )
      
      if (attributeExists) {
        setAttributeError('This attribute-value combination already exists')
        return
      }
      
      // Check if we're adding a new attribute
      let selectedAttribute = attributes.find(attr => 
        attr.name.toLowerCase() === newAttributeName.toLowerCase()
      )
      
      // If it's a new attribute, create it in the database
      if (!selectedAttribute && !isCreatingAttribute) {
        setIsCreatingAttribute(true)
        selectedAttribute = await createAttribute(newAttributeName)
        setIsCreatingAttribute(false)
      }

      // Check if we're adding a new value
      const existingValues = getAttributeValues(newAttributeName.toLowerCase())
      const valueExists = existingValues.some(v => 
        v.value.toLowerCase() === newAttributeValue.toLowerCase()
      )
      
      // If it's a new value, create it in the database
      if (!valueExists && selectedAttribute && !isCreatingValue) {
        setIsCreatingValue(true)
        await createAttributeValue(selectedAttribute.id, newAttributeValue)
        setIsCreatingValue(false)
        
        // Refresh the values for this attribute
        await fetchAttributeValues(selectedAttribute.id, newAttributeName.toLowerCase())
      }

      const newAttribute: SimpleAttribute = {
        name: newAttributeName,
        value: newAttributeValue
      }

      // Add to custom attributes array
      const updatedCustomAttributes = [...formData.custom_attributes, newAttribute]
      updateFormData('custom_attributes', updatedCustomAttributes)

      // Update form data for backward compatibility for size and color
      if (newAttributeName.toLowerCase() === 'size') {
        updateFormData('size', newAttributeValue)
      } else if (newAttributeName.toLowerCase() === 'color') {
        updateFormData('color', newAttributeValue)
      }

      // Reset form
      setNewAttributeName('')
      setNewAttributeValue('')
    } catch (error: any) {
      console.error('Error adding attribute:', error)
      setAttributeError(error.message || 'Failed to add attribute')
    }
  }

  const removeAttribute = (index: number) => {
    const attributeToRemove = formData.custom_attributes[index]
    const updatedCustomAttributes = formData.custom_attributes.filter((_, i) => i !== index)
    updateFormData('custom_attributes', updatedCustomAttributes)

    // Clear form data for backward compatibility
    if (attributeToRemove.name.toLowerCase() === 'size') {
      updateFormData('size', '')
    } else if (attributeToRemove.name.toLowerCase() === 'color') {
      updateFormData('color', '')
    }
  }

  const getValueOptions = (attributeName: string) => {
    return getAttributeValues(attributeName)
  }

  return (
    <div className="space-y-3">
      <div className="border-t border-gray-200 pt-4">
        {/* Add New Attribute */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-2 items-end">
          <FormField label="Attribute Type">
            <SimpleDropdown
              value={newAttributeName}
              onValueChange={setNewAttributeName}
              placeholder="Select attribute type"
              options={attributeOptions}
              allowCustom={true}
              customPlaceholder="Enter custom attribute"
              className="h-8 text-xs"
              optionClassName="text-xs"
              size="xs"
              textsize="xs"
            />
          </FormField>

          <FormField label="Value">
            {newAttributeName && getValueOptions(newAttributeName).length > 0 ? (
              <SimpleDropdown
                value={newAttributeValue}
                onValueChange={setNewAttributeValue}
                placeholder="Select value"
                options={getValueOptions(newAttributeName)}
                allowCustom={true}
                customPlaceholder="Enter custom value"
                className="h-8 text-xs"
                optionClassName="text-xs"
                size="xs"
                textsize="xs"
              />
            ) : (
              <Input
                value={newAttributeValue}
                onChange={(e) => setNewAttributeValue(e.target.value)}
                placeholder="Enter attribute value"
                className={getInputClasses(false)}
              />
            )}
          </FormField>

          <div className="flex items-end h-full">
            <Button
              type="button"
              onClick={addAttribute}
              disabled={!newAttributeName || !newAttributeValue || isCreatingAttribute || isCreatingValue}
              className="h-8 px-5 text-xs"
            >
              {isCreatingAttribute || isCreatingValue ? 'Adding...' : 'Add'}
            </Button>
          </div>
          {attributeError && (
            <div className="col-span-3 text-red-500 text-xs mt-1">
              {attributeError}
            </div>
          )}
        </div>

        {/* Existing Attributes */}
        {formData.custom_attributes.length > 0 && (
          <div className="mt-3">
            <div className="text-xs text-gray-500 mb-2">Selected Attributes:</div>
            <div className="flex flex-wrap gap-2">
              {formData.custom_attributes.map((attribute, index) => (
                <div 
                  key={index} 
                  className="inline-flex items-center gap-1.5 bg-black text-white rounded-full px-2.5 py-1 text-xs font-medium transition-all"
                >
                  <span className="capitalize">
                    {attribute.name}:
                  </span>
                  <span className="font-normal capitalize">
                    {attribute.value}
                  </span>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeAttribute(index)}
                    className="h-4 w-4 p-0 text-white hover:text-gray-300 hover:bg-transparent"
                  >
                    <X className="w-2.5 h-2.5" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}