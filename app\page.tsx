'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'
import { Loader2 } from 'lucide-react'

export default function Home() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [redirecting, setRedirecting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Set a timeout to prevent indefinite loading
    const timeout = setTimeout(() => {
      if (loading) {
        setError('Authentication is taking longer than expected. Please refresh the page.')
      }
    }, 15000) // 15 second timeout

    return () => clearTimeout(timeout)
  }, [loading])

  useEffect(() => {
    if (!loading && !redirecting) {
      setRedirecting(true)
      // Ensure we have the final authentication state before redirecting
      if (user) {
        router.replace('/dashboard')
      } else {
        // Redirect to landing page instead of sign-in page
        router.replace('/landing')
      }
    }
  }, [user, loading, router, redirecting])

  // Handle network errors or other issues
  useEffect(() => {
    const handleOnline = () => {
      if (error) {
        setError(null)
        window.location.reload()
      }
    }

    window.addEventListener('online', handleOnline)
    return () => window.removeEventListener('online', handleOnline)
  }, [error])

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center p-6 max-w-md">
          <div className="text-2xl font-bold text-red-600 mb-2">Connection Issue</div>
          <p className="text-gray-700 mb-4">
            {error}
          </p>
          <div className="flex flex-col gap-2">
            <button 
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
              onClick={() => window.location.reload()}
            >
              Reload Page
            </button>
            <button 
              className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
              onClick={() => router.replace('/landing')}
            >
              Go to Home
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="flex flex-col items-center gap-2">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="text-lg">Loading...</span>
        <span className="text-sm text-gray-500">Initializing application</span>
      </div>
    </div>
  )
}