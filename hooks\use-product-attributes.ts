import { useState, useEffect } from 'react'
import { getSupabaseClient } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'

export interface ProductAttribute {
  id: string
  name: string
  created_at: string
  updated_at: string
}

export interface ProductAttributeValue {
  value: string
  label: string
}

export function useProductAttributes() {
  const { organizationId } = useAuth()
  const [attributes, setAttributes] = useState<ProductAttribute[]>([])
  const [attributeValues, setAttributeValues] = useState<Record<string, ProductAttributeValue[]>>({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch attributes
  useEffect(() => {
    if (!organizationId) return

    const fetchAttributes = async () => {
      try {
        setLoading(true)
        const supabase = getSupabaseClient()
        
        // Fetch product attributes
        const { data, error } = await supabase
          .from('product_attributes')
          .select('*')
          .eq('organization_id', organizationId)
          .order('name')

        if (error) throw error

        setAttributes(data || [])
        setError(null)
        
        // Fetch values for all attributes
        if (data && data.length > 0) {
          for (const attribute of data) {
            await fetchAttributeValues(attribute.id, attribute.name)
          }
        }
      } catch (err) {
        console.error('Error fetching product attributes:', err)
        setError('Failed to fetch product attributes')
      } finally {
        setLoading(false)
      }
    }

    fetchAttributes()
  }, [organizationId])

  // Function to fetch values for a specific attribute
  const fetchAttributeValues = async (attributeId: string, attributeName: string) => {
    if (!organizationId) return []

    try {
      const supabase = getSupabaseClient()
      
      const { data, error } = await supabase
        .from('product_attribute_values')
        .select('value')
        .eq('organization_id', organizationId)
        .eq('attribute_id', attributeId)
        .order('value')

      if (error) throw error

      const values = data.map((item) => ({ value: item.value, label: item.value }))
      
      // Update the attribute values state
      setAttributeValues(prev => ({
        ...prev,
        [attributeName.toLowerCase()]: values
      }))

      return values
    } catch (err) {
      console.error(`Error fetching values for attribute ${attributeName}:`, err)
      return []
    }
  }

  // Function to create a new attribute
  const createAttribute = async (name: string) => {
    if (!organizationId) return null

    try {
      const supabase = getSupabaseClient()
      
      const { data, error } = await supabase
        .from('product_attributes')
        .insert({ 
          organization_id: organizationId, 
          name 
        })
        .select()
        .single()

      if (error) {
        // Handle duplicate attribute name error
        if (error.code === '23505') {
          throw new Error('Attribute with this name already exists')
        }
        throw error
      }

      // Add the new attribute to the state
      setAttributes(prev => [...prev, data])
      
      return data
    } catch (err: any) {
      console.error('Error creating product attribute:', err)
      throw err
    }
  }

  // Function to create a new attribute value
  const createAttributeValue = async (attributeId: string, value: string) => {
    if (!organizationId) return null

    try {
      const supabase = getSupabaseClient()
      
      const { data, error } = await supabase
        .from('product_attribute_values')
        .insert({
          organization_id: organizationId,
          attribute_id: attributeId,
          value,
        })
        .select()
        .single()

      if (error) {
        // Handle duplicate value error
        if (error.code === '23505') {
          // Value already exists, not an error we need to show
          return null
        }
        throw error
      }

      return data
    } catch (err: any) {
      console.error('Error creating product attribute value:', err)
      throw err
    }
  }

  // Get attribute values for a specific attribute name
  const getAttributeValues = (attributeName: string) => {
    return attributeValues[attributeName.toLowerCase()] || []
  }

  return {
    attributes,
    getAttributeValues,
    fetchAttributeValues,
    createAttribute,
    createAttributeValue,
    loading,
    error
  }
}