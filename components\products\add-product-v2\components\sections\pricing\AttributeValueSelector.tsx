'use client'

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { SimpleDropdown } from '@/components/ui/simple-dropdown'
import { Badge } from '@/components/ui/badge'
import { Plus, X, Check } from 'lucide-react'
import { createSupabaseClient } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { useToast } from '@/components/ui/use-toast'
import { designSystem, getInputClasses, getButtonClasses, getBadgeClasses } from '../../../config/design-system'
import { FormField } from '../../shared/FormField'

interface AttributeValueSelectorProps {
  attributeOption: { value: string; label: string }
  onComplete: (attributeName: string, values: string[]) => void
  onCancel: () => void
}

export function AttributeValueSelector({ attributeOption, onComplete, onCancel }: AttributeValueSelectorProps) {
  const { organizationId } = useAuth()
  const { toast } = useToast()
  
  const [selectedValues, setSelectedValues] = useState<string[]>([])
  const [availableValues, setAvailableValues] = useState<Array<{value: string, label: string}>>([])
  const [loadingValues, setLoadingValues] = useState(false)
  const [newValue, setNewValue] = useState('')
  const [addingCustomValue, setAddingCustomValue] = useState(false)

  // Load existing values for this attribute
  useEffect(() => {
    loadAttributeValues()
  }, [attributeOption.value])

  const loadAttributeValues = async () => {
    if (!organizationId) return
    
    setLoadingValues(true)
    try {
      const supabase = createSupabaseClient()
      const { data, error } = await supabase
        .from('product_attribute_values')
        .select('value')
        .eq('attribute_id', attributeOption.value)
        .eq('organization_id', organizationId)
        .order('value')

      if (error) throw error

      // Deduplicate values
      const uniqueValues = Array.from(new Set(data?.map(v => v.value) || []))
      const valueOptions = uniqueValues.map(value => ({
        value: value,
        label: value
      }))

      setAvailableValues(valueOptions)
    } catch (error) {
      console.error('Error loading attribute values:', error)
    } finally {
      setLoadingValues(false)
    }
  }

  const addValue = (value: string) => {
    if (!value.trim() || selectedValues.includes(value)) return
    setSelectedValues(prev => [...prev, value])
  }

  const removeValue = (value: string) => {
    setSelectedValues(prev => prev.filter(v => v !== value))
  }

  const addCustomValue = async () => {
    if (!newValue.trim()) return

    // Add to selected values immediately
    addValue(newValue)

    // Save to database if it's a user-defined attribute
    if (organizationId) {
      try {
        const supabase = createSupabaseClient()
        const { error } = await supabase
          .from('product_attribute_values')
          .insert({
            attribute_id: attributeOption.value,
            value: newValue,
            organization_id: organizationId
          })

        if (error) throw error

        // Add to available values for future use
        setAvailableValues(prev => [...prev, { value: newValue, label: newValue }])
      } catch (error) {
        console.error('Error saving custom value:', error)
        toast({
          title: "Warning",
          description: "Value added locally but not saved to database",
          variant: "destructive"
        })
      }
    }

    setNewValue('')
    setAddingCustomValue(false)
  }

  const handleComplete = () => {
    if (selectedValues.length === 0) {
      toast({
        title: "Error",
        description: "Please select at least one value for this attribute",
        variant: "destructive"
      })
      return
    }
    onComplete(attributeOption.label, selectedValues)
  }

  return (
    <div className="space-y-4 p-4 border border-blue-200 bg-blue-50 rounded-lg">
      <div className="flex items-center justify-between">
        <h4 className={`${designSystem.typography.label} text-blue-800`}>
          Configure "{attributeOption.label}" Values
        </h4>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={onCancel}
          className="text-blue-600 hover:text-blue-800"
        >
          <X className="w-4 h-4" />
        </Button>
      </div>

      {/* Value Selection */}
      <div className="space-y-3">
        <FormField label="Select Values">
          <SimpleDropdown
            options={availableValues}
            value=""
            onValueChange={addValue}
            placeholder="Select a value"
            disabled={loadingValues}
          />
        </FormField>

        {/* Selected Values */}
        {selectedValues.length > 0 && (
          <div className="space-y-2">
            <p className={`${designSystem.typography.caption} text-blue-700`}>
              Selected values:
            </p>
            <div className="flex flex-wrap gap-2">
              {selectedValues.map((value, index) => (
                <Badge
                  key={index}
                  className={`${getBadgeClasses('secondary')} group cursor-pointer`}
                  onClick={() => removeValue(value)}
                >
                  {value}
                  <X className="w-3 h-3 ml-1 opacity-50 group-hover:opacity-100" />
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Add Custom Value */}
        {addingCustomValue ? (
          <div className="flex gap-2">
            <Input
              value={newValue}
              onChange={(e) => setNewValue(e.target.value)}
              placeholder={`Enter custom ${attributeOption.label.toLowerCase()} value`}
              className={getInputClasses()}
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  addCustomValue()
                } else if (e.key === 'Escape') {
                  setAddingCustomValue(false)
                  setNewValue('')
                }
              }}
              autoFocus
            />
            <Button
              type="button"
              onClick={addCustomValue}
              disabled={!newValue.trim()}
              className={getButtonClasses('secondary')}
            >
              <Plus className="w-4 h-4" />
            </Button>
            <Button
              type="button"
              onClick={() => {
                setAddingCustomValue(false)
                setNewValue('')
              }}
              variant="outline"
              className="px-3"
            >
              Cancel
            </Button>
          </div>
        ) : (
          <Button
            type="button"
            onClick={() => setAddingCustomValue(true)}
            variant="outline"
            className="w-full justify-center"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add custom {attributeOption.label.toLowerCase()} value
          </Button>
        )}
      </div>

      {/* Actions */}
      <div className="flex gap-2 pt-2 border-t border-blue-200">
        <Button
          type="button"
          onClick={handleComplete}
          disabled={selectedValues.length === 0}
          className={getButtonClasses('primary')}
        >
          <Check className="w-4 h-4 mr-2" />
          Add Attribute ({selectedValues.length} value{selectedValues.length !== 1 ? 's' : ''})
        </Button>
        <Button
          type="button"
          onClick={onCancel}
          variant="outline"
        >
          Cancel
        </Button>
      </div>
    </div>
  )
}
